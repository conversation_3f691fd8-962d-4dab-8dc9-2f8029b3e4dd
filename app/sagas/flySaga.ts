/* eslint-disable yield-star-spacing */
import { all, call, put, select, takeLatest } from "redux-saga/effects"
import { API, graphqlOperation } from "aws-amplify"
import {
  flyCityCodes,
  getFlyFilters,
  getIntoCityOrAirport,
  myTravelInsertFlight,
  getMyTravelFlightDetails,
  deleteMyTravelFlightDetail,
  getCRTBenefitCard,
  getTravelChecklists,
  getConfigQuery,
  searchFlights,
  searchAllFlights,
  getFlights,
  getDetailFlight,
  getFlyEarlierListQuery,
  getAppscapadeLuckyDrawEligibleQuery,
  getAppscapadeBannerQuery,
  getDetailFlightForScanBoardingPass,
  getFlightFilterOptionsQuery,
  getGamificationGameChanceQuery,
  getMaintenanceConfigurations,
  getDetailFlightForScanBoardingPassV2,
  getDetailFlightV2,
} from "../models/queries"
import { FlyCreators, FlyTypes } from "app/redux/flyRedux"
import DeviceInfo from "react-native-device-info"

import path from "app/services/api/apis.json"
import restApi from "app/services/api/request"

import { env } from "app/config/env-params"
import isEmpty from "lodash/isEmpty"
import forEach from "lodash/forEach"
import isArray from "lodash/isArray"
import set from "lodash/set"
import moment from "moment"
import momentTz from 'moment-timezone'
import {
  DateFormats,
  getCurrentDateSingapore,
  getCurrentTimeSingapore,
  getDateSingapore,
} from "app/utils/date-time/date-time"
import { FlightNavigationType, FlightDirection } from "app/screens/fly/flights/flight-props"
import SearchActions from "app/redux/searchRedux"
import { RootState, store } from "app/redux/store"
import {
  REMOTE_CONFIG_FLAGS,
  isFlagON,
} from "app/services/firebase/remote-config"
import { handleCondition } from "app/utils"
import { analyticsLogEvent, ANALYTICS_LOG_EVENT_NAME, dtACtionLogEvent, dtManualActionEvent, FE_LOG_PREFIX } from "app/services/firebase/analytics"
import { FB_EVENT_NAME } from "app/services/facebook/event-name"
import { AppEventsLogger } from "react-native-fbsdk-next"
import { getEnvSetting } from "app/utils/env-settings"
import { DisplayModule, MaintenanceType } from "app/models/enum"
import PageConfigCreators from "app/redux/pageConfigRedux"
import { MytravelCreators, MytravelTypes } from "app/redux/mytravelRedux"
import { convertStringValue, DT_ANALYTICS_LOG_EVENT_NAME, dtActionEvent } from "app/services/firebase/analytics"
import { syncGetFlyCityCode } from "app/utils/sync_tasks/data-sync-handler"

const deviceId = DeviceInfo.getUniqueIdSync()
const deviceName = DeviceInfo.getDeviceNameSync()

export const getQueryFilter = (filters) => {
  if (filters && Array.isArray(filters) && filters?.length > 0) {
    return {
      terminal: filters
        .map((item) => {
          if (item.includes("terminal-")) {
            return item.replace("terminal-", "")
          }
          return item
        })
        ?.join(","),
    }
  }
  return { terminal: "" }
}

export function* flyLandingQuery({ direction, pageSize = "30", nextToken = "", filters = [] }) {
  const scheduledDate = getCurrentDateSingapore()
  const variables = {
    direction,
    page_size: pageSize,
    scheduled_date: scheduledDate,
    ...getQueryFilter(filters),
    ...(nextToken ? { next_token: nextToken } : {}),
  }
  const response = yield call(restApi, {
    url: env()?.APPSYNC_GRAPHQL_URL,
    method: "post",
    data: graphqlOperation(getFlights, variables),
    parameters: {},
    headers: {
      "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
    },
  })
  return response?.data
}

export const handleScheduledDateForFly = (isFilter, isLoadFlightAfter24h, scheduledDate) => {
  if (isLoadFlightAfter24h) {
    const currentDate = getCurrentTimeSingapore()
    const nextDateTime = moment(currentDate).add(1, "day").format("YYYY-MM-DD HH:mm").split(" ")
    return nextDateTime[0]
  }
  return isFilter ? `${scheduledDate}*` : scheduledDate
}

export const handleScheduledTimeForFly = (isLoadFlightAfter24h) => {
  if (isLoadFlightAfter24h) {
    const currentDate = getCurrentTimeSingapore()
    const nextDateTime = moment(currentDate).add(1, "day").format("YYYY-MM-DD HH:mm").split(" ")
    return nextDateTime[1]
  }
  return ""
}

export const handleScheduledDateForEarlierFly = (isLoadFlightAfter24h, scheduledDate) => {
  if (isLoadFlightAfter24h) {
    const nextDateTime = moment().add(1, "day").format("YYYY-MM-DD HH:mm").split(" ")
    return nextDateTime[0]
  }
  return scheduledDate
}

export const handleScheduledTimeForEarlierFly = (isLoadFlightAfter24h) => {
  if (isLoadFlightAfter24h) {
    const nextDateTime = moment().add(1, "day").format("YYYY-MM-DD HH:mm").split(" ")
    return nextDateTime[1]
  }
  return ""
}

export function* flyListQuery({
  direction,
  nextToken = "",
  filterDate = moment(),
  filters,
  isFilter = false,
  isLoadFlightAfter24h = false,
}) {
  let scheduledDate = getDateSingapore(filterDate)
  const currentDate = moment().format(DateFormats.YearMonthDay)
  const isPast = new Date(scheduledDate) < new Date(currentDate)
  if (isPast) {
    scheduledDate = currentDate
  }
  const body = {
    direction,
    ...(nextToken ? { next_token: nextToken } : {}),
    page_size: 30,
    scheduled_date: handleScheduledDateForFly(isFilter, isLoadFlightAfter24h, scheduledDate),
    scheduled_time: handleScheduledTimeForFly(isLoadFlightAfter24h),
    ...getQueryFilter(filters),
  }

  const response = yield call(restApi, {
    url: env()?.APPSYNC_GRAPHQL_URL,
    method: "post",
    data: graphqlOperation(getFlights, { ...body }),
    parameters: {},
    headers: {
      "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
    },
  })
  return response?.data
}

export function* flyEarlierListQuery({
  direction,
  prev = "true",
  previousToken = "",
  filterDate = moment(),
  filters,
  pageSize = "30",
  isLoadFlightAfter24h = false,
}) {
  let scheduledDate = getDateSingapore(filterDate)
  const currentDate = moment().format(DateFormats.YearMonthDay)
  const isPast = new Date(scheduledDate) < new Date(currentDate)
  if (isPast) {
    scheduledDate = currentDate
  }
  const body = handleCondition(
    previousToken,
    {
      direction,
      next_token: previousToken,
      prev,
      scheduled_date: handleScheduledDateForEarlierFly(isLoadFlightAfter24h, scheduledDate),
      scheduled_time: handleScheduledTimeForEarlierFly(isLoadFlightAfter24h),
      page_size: pageSize,
      ...getQueryFilter(filters),
    },
    {
      direction,
      prev,
      scheduled_date: handleScheduledDateForEarlierFly(isLoadFlightAfter24h, scheduledDate),
      scheduled_time: handleScheduledTimeForEarlierFly(isLoadFlightAfter24h),
      page_size: pageSize,
      ...getQueryFilter(filters),
    },
  )

  const response = yield call(restApi, {
    url: env()?.APPSYNC_GRAPHQL_URL,
    method: "post",
    data: graphqlOperation(getFlyEarlierListQuery, { ...body }),
    parameters: {},
    headers: {
      "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
    },
  })
  return response?.data
}

export function* getFlyLandingDeparture(action: any) {
  const { direction, flightRequestType, filters } = action
  try {
    const dtAction = dtActionEvent(DT_ANALYTICS_LOG_EVENT_NAME.DT_FLIGHT_LANDING_DEPARTURE, {
      device_name: convertStringValue(deviceName),
      device_id: deviceId,
      direction: FlightDirection.departure,
    })
    const featureFlag = "v2"
    const pageSize = "30"
    const response = yield* flyLandingQuery({ direction, filters, pageSize })
    dtAction.reportIntValue("data_length", response?.data?.getFlights?.flights?.length || 0)
    const myTravelFlightsPayload = yield select((appState: RootState) => appState.mytravelReducer.myTravelFlightsPayload)
    yield put(FlyCreators.flyLandingDepartureSuccess(response, featureFlag, myTravelFlightsPayload))
    dtAction.leaveAction()
  } catch (err) {
    yield put(FlyCreators.flyLandingDepartureFailure(err.errors, flightRequestType))
  }
}
export function* getFlyLandingArrival(action: any) {
  const { direction, flightRequestType, filters } = action
  try {
    const dtAction = dtActionEvent(DT_ANALYTICS_LOG_EVENT_NAME.DT_FLIGHT_LANDING_ARRIVAL, {
      device_name: convertStringValue(deviceName),
      device_id: deviceId,
      direction: FlightDirection.arrival,
    })
    const featureFlag = "v2"
    const pageSize = "30"
    const response = yield* flyLandingQuery({ direction, filters, pageSize })
    dtAction.reportIntValue("data_length", response?.data?.getFlights?.flights?.length || 0)
    const flyCodesPayload = yield select((appState: RootState) => appState.mytravelReducer.myTravelFlightsPayload)
    yield put(FlyCreators.flyLandingArrivalSuccess(response, featureFlag, flyCodesPayload))
    dtAction.leaveAction()
  } catch (err) {
    yield put(FlyCreators.flyLandingArrivalFailure(err.errors, flightRequestType))
  }
}
/**
 * This function will be trigger by {@link syncGetFlyCityCode} , should not call this function directly. 
 **/ 
export function* getFlyCityCode() {
  const dtAction = dtManualActionEvent(DT_ANALYTICS_LOG_EVENT_NAME.DT_GET_CITY_CODES)
  try {
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(flyCityCodes),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    dtAction.reportStringValue('status', 'success')
    yield put(FlyCreators.flyCityCodeSuccess(response))
  } catch (err) {
    dtAction.reportStringValue('status', 'failed')
    yield put(FlyCreators.flyCityCodeFailure(err.message))
  } finally {
    dtAction.leaveAction()
  }
}

export function* getFlyDepartureList(action) {
  const {
    direction,
    flightRequestType,
    filterDate,
    filters,
    isFilter,
    isLoadFlightAfter24h,
  } = action
  try {
    const response = yield* flyListQuery({
      direction,
      filterDate,
      filters,
      isFilter,
      isLoadFlightAfter24h,
    })
    const myTravelFlightsPayload = yield select((appState: RootState) => appState.mytravelReducer.myTravelFlightsPayload)
    yield put(
      FlyCreators.flyDepartureListSuccess(
        response,
        handleCondition(isLoadFlightAfter24h, false, isFilter),
        myTravelFlightsPayload
      ),
    ) // If isLoadFlightAfter24h is true, not using isFilter
  } catch (err) {
    yield put(FlyCreators.flyDepartureListFailure(err.message, flightRequestType))
  }
}
export function* searchAllFlightsRequest(action) {
  const { text = "", filterDate } = action
  const query = {
    text,
    filter: {
      scheduled_date: filterDate,
    },
    page_number: 1,
    page_size: 3,
  }
  try {
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(searchAllFlights, query),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    const myTravelFlightsPayload = yield select((appState: RootState) => appState.mytravelReducer.myTravelFlightsPayload)
    yield put(FlyCreators.searchFlightsSuccess(response?.data, myTravelFlightsPayload))
  } catch (err) {
    yield put(FlyCreators.searchFlightsFailure(err.message))
  }
}

const convertSearchDetailFlightsResult = (searchResultData, searchingDirection, shouldIgnoreDirectionAndDate) => {
  const responseData = { ...searchResultData }
  const flightsData = searchResultData?.data?.flights?.items
  const firstResultFlight = flightsData?.[0]
  const firstResultFlightDirection = firstResultFlight?.direction
  const firstResultFlightScheduledDate = firstResultFlight?.scheduled_date
  const isFirstFlightIsDepartureFlight = firstResultFlightDirection === FlightDirection.departure

  if (shouldIgnoreDirectionAndDate) {
    responseData.shouldNavigateToDepartureTab = isFirstFlightIsDepartureFlight
  }

  let filteredFlightsData = []
  if (flightsData?.length) {
    for (let i = 0; i < flightsData.length; i++) {
      const flightElement = flightsData[i]
      const flightElementDirection = flightElement?.direction
      const flightElementScheduledDate = flightElement?.scheduled_date
      const shouldAddToFilteredFlights =
        flightElementDirection === searchingDirection &&
        moment(firstResultFlightScheduledDate).isSame(flightElementScheduledDate, "day")
      if (shouldAddToFilteredFlights) {
        filteredFlightsData = [...filteredFlightsData, flightElement]
      }
    }
  }
  set(responseData, ["data", "flights", "items"], filteredFlightsData)
  return responseData
}
export function* searchDetailFlightsRequest(action) {
  const { direction, pageNumber, filterDate, text, isPagingRequest, filters = [], shouldIgnoreDirectionAndDate } = action

  const query = {
    text,
    direction,
    filterDate,
    pageNumber,
    pageSize: 30,
    isPagingRequest,
    filters,
  }

  try {
    let scheduledDate = moment(filterDate).format(DateFormats.YearMonthDay)
    const currentDate = moment().format(DateFormats.YearMonthDay)
    const isPast = new Date(scheduledDate) < new Date(currentDate)
    if (isPast) {
      scheduledDate = currentDate
    }
    const filterLocation = getQueryFilter(filters)
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(searchFlights, {
        text,
        filter: {
          ...(!shouldIgnoreDirectionAndDate && {
            direction,
            ...(filterDate && { scheduled_date: scheduledDate }),
          }),
          ...(filterLocation?.terminal?.length > 0 ? filterLocation : {}),
        },
        page_number: pageNumber,
        page_size: 30,
      }),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })

    if (response?.data?.data?.flights?.items === null) {
      throw response
    }
    const myTravelFlightsPayload = yield select((appState: RootState) => appState.mytravelReducer.myTravelFlightsPayload)
    yield put(SearchActions.addSearchKeywordToCollection(text, response?.data?.data?.flights?.items?.length || 0))
    const searchDetailFightsSuccessData = convertSearchDetailFlightsResult({...response?.data, query}, direction, shouldIgnoreDirectionAndDate)
    yield put(FlyCreators.searchDetailFlightsSuccess(searchDetailFightsSuccessData, myTravelFlightsPayload))
  } catch (err) {
    yield put(FlyCreators.searchDetailFlightsFailure({ error: err.message, query }))
  }
}
export function* getDeparturePaginationResult(action) {
  const { direction, nextToken, filterDate, filters, isFilter, isLoadFlightAfter24h } = action
  try {
    const response = yield* flyListQuery({
      direction,
      nextToken,
      filterDate,
      filters,
      isFilter,
      isLoadFlightAfter24h,
    })
    const myTravelFlightsPayload = yield select((appState: RootState) => appState.mytravelReducer.myTravelFlightsPayload)
    yield put(FlyCreators.flyDeparturePaginationSuccess(response, myTravelFlightsPayload))
  } catch (err) {
    yield put(FlyCreators.flyDeparturePaginationFailure(err.message))
  }
}
export function* getDepartureEarlierPaginationResult(action) {
  const { direction, previousToken, filterDate, filters, isLoadFlightAfter24h } = action
  try {
    const responseEerlier = yield* flyEarlierListQuery({
      direction,
      prev: "true",
      previousToken,
      filterDate,
      filters,
      isLoadFlightAfter24h,
    })
    if (!isEmpty(responseEerlier?.data?.getFlights)) {
      const earlierDataFly = responseEerlier?.data?.getFlights
      const dataFly = {
        data: {
          getFlights: {
            flights: [...earlierDataFly?.flights?.reverse()],
            previous_token: earlierDataFly?.next_token,
          },
        },
      }
      const myTravelFlightsPayload = yield select((appState: RootState) => appState.mytravelReducer.myTravelFlightsPayload)
      yield put(FlyCreators.flyDeparturePaginationEarlierSuccess(dataFly, myTravelFlightsPayload))
    } else throw responseEerlier
  } catch (err) {
    yield put(FlyCreators.flyDeparturePaginationEarlierFailure(err.message))
  }
}

export function* getFlyArrivalList(action) {
  const {
    direction,
    flightRequestType,
    filterDate,
    filters,
    isFilter,
    isLoadFlightAfter24h,
  } = action
  try {
    const response = yield* flyListQuery({
      direction,
      filterDate,
      filters,
      isFilter,
      isLoadFlightAfter24h,
    })
    const myTravelFlightsPayload = yield select((appState: RootState) => appState.mytravelReducer.myTravelFlightsPayload)
    yield put(
      FlyCreators.flyArrivalListSuccess(
        response,
        handleCondition(isLoadFlightAfter24h, false, isFilter),
        myTravelFlightsPayload
      ),
    ) // If isLoadFlightAfter24h is true, not using isFilter
  } catch (err) {
    yield put(FlyCreators.flyArrivalListFailure(err.message, flightRequestType))
  }
}
export function* getArrivalPaginationResult(action) {
  const { direction, nextToken, filterDate, filters, isFilter, isLoadFlightAfter24h } = action
  try {
    const response = yield* flyListQuery({
      direction,
      nextToken,
      filterDate,
      filters,
      isFilter,
      isLoadFlightAfter24h,
    })
    yield put(FlyCreators.flyArrivalPaginationSuccess(response))
  } catch (err) {
    yield put(FlyCreators.flyArrivalPaginationFailure(err.message))
  }
}
export function* getArrivalEarlierPaginationResult(action) {
  const { direction, previousToken, filterDate, filters, isLoadFlightAfter24h } = action
  try {
    const responseEerlier = yield* flyEarlierListQuery({
      direction,
      prev: "true",
      previousToken,
      filterDate,
      filters,
      isLoadFlightAfter24h,
    })
    if (!isEmpty(responseEerlier?.data?.getFlights)) {
      const earlierDataFly = responseEerlier?.data?.getFlights
      const dataFly = {
        data: {
          getFlights: {
            flights: [...earlierDataFly?.flights?.reverse()],
            previous_token: earlierDataFly?.next_token,
          },
        },
      }
      const myTravelFlightsPayload = yield select((appState: RootState) => appState.mytravelReducer.myTravelFlightsPayload)
      yield put(FlyCreators.flyArrivalPaginationEarlierSuccess(dataFly, myTravelFlightsPayload))
    } else throw responseEerlier
  } catch (err) {
    yield put(FlyCreators.flyArrivalPaginationEarlierFailure(err.message))
  }
}
export function* getDepartureEarlierInitialQuery(action) {
  const { direction, filterDate, filters, isLoadFlightAfter24h } = action
  try {
    const responseEerlier = yield* flyEarlierListQuery({
      direction,
      prev: "true",
      filterDate,
      filters,
      isLoadFlightAfter24h,
    })
    const responseCurrent = yield* flyEarlierListQuery({
      direction,
      prev: "false",
      filterDate,
      filters,
      isLoadFlightAfter24h,
    })
    if (
      !isEmpty(responseEerlier?.data?.getFlights) &&
      !isEmpty(responseCurrent?.data?.getFlights)
    ) {
      const currentDataFly = responseCurrent?.data?.getFlights
      const earlierDataFly = responseEerlier?.data?.getFlights
      const dataFly = {
        data: {
          getFlights: {
            flights: [...earlierDataFly?.flights?.reverse(), ...currentDataFly?.flights],
            next_token: currentDataFly?.next_token,
            previous_token: earlierDataFly?.next_token,
          },
        },
      }
      const myTravelFlightsPayload = yield select((appState: RootState) => appState.mytravelReducer.myTravelFlightsPayload)
      yield put(FlyCreators.flyDepartureEarlierInitialSuccess(dataFly, myTravelFlightsPayload))
    } else throw responseEerlier || responseCurrent
  } catch (err) {
    yield put(FlyCreators.flyDepartureEarlierInitialFailure(err.message))
  }
}
export function* getArrivalEarlierInitialQuery(action) {
  const { direction, filterDate, filters, isLoadFlightAfter24h } = action
  try {
    const responseEerlier = yield* flyEarlierListQuery({
      direction,
      prev: "true",
      filterDate,
      filters,
      isLoadFlightAfter24h,
    })
    const responseCurrent = yield* flyEarlierListQuery({
      direction,
      prev: "false",
      filterDate,
      filters,
      isLoadFlightAfter24h,
    })
    if (
      !isEmpty(responseEerlier?.data?.getFlights) &&
      !isEmpty(responseCurrent?.data?.getFlights)
    ) {
      const currentDataFly = responseCurrent?.data?.getFlights
      const earlierDataFly = responseEerlier?.data?.getFlights
      const dataFly = {
        data: {
          getFlights: {
            flights: [...earlierDataFly?.flights?.reverse(), ...currentDataFly?.flights],
            next_token: currentDataFly?.next_token,
            previous_token: earlierDataFly?.next_token,
          },
        },
      }
      const myTravelFlightsPayload = yield select((appState: RootState) => appState.mytravelReducer.myTravelFlightsPayload)
      yield put(FlyCreators.flyArrivalEarlierInitialSuccess(dataFly, myTravelFlightsPayload))
    } else throw responseEerlier || responseCurrent
  } catch (err) {
    yield put(FlyCreators.flyArrivalEarlierInitialFailure(err.message))
  }
}

export function* getFlyLocationFilterParameter() {
  try {
    const response = yield call([API, "graphql"], graphqlOperation(getFlyFilters))
    yield put(FlyCreators.flyLocationFilterParametersSuccess(response))
  } catch (err) {
    yield put(FlyCreators.flyLocationFilterParametersFailure(err.errors))
  }
}

const formatResponseGetDetailFlight = (response, isFromScanBoardingPass, enableGTTD) => {
  const flightData = handleCondition(
    isFromScanBoardingPass,
    [
      {
        ...response?.getFlightDetailsForScanBoardingPass?.flightInfo,
        isSaved: response?.getFlightDetailsForScanBoardingPass?.myTravelInfo?.is_saved,
        isPassenger: response?.getFlightDetailsForScanBoardingPass?.myTravelInfo?.is_passenger,
        baggageTracking:
          response?.getFlightDetailsForScanBoardingPass?.myTravelInfo?.baggage_tracking,
        groundTransport: enableGTTD ? response?.getFlightDetailsForScanBoardingPass?.groundTransport : null
      },
    ],
    [
      {
        ...response?.getFlightDetails?.flightInfo,
        isSaved: response?.getFlightDetails?.myTravelInfo?.is_saved,
        isPassenger: response?.getFlightDetails?.myTravelInfo?.is_passenger,
        baggageTracking: response?.getFlightDetails?.myTravelInfo?.baggage_tracking,
        groundTransport: enableGTTD ? response?.getFlightDetails?.groundTransport : null
      },
    ],
  )
  return {
    getEarlyCheckin: response?.getEarlyCheckin,
    getFlights: {
      next_token: null,
      flights: flightData,
    },
    getOnlineCheckin: response?.getOnlineCheckin,
  }
}

export function* getFlightDetailsQuery(action) {
  const {
    direction,
    flightNumber,
    scheduledDate,
    flightRequestType,
    airlineCode,
    flightStatus,
    isFromScanBoardingPass,
  } = action

  try {
    const myTravelUidEnabled = true
    const enableGTTD = isFlagON(REMOTE_CONFIG_FLAGS.GROUND_TRANSPORT_TIMINGS_DISPLAY)
    const enableECIV2 = isFlagON(REMOTE_CONFIG_FLAGS.ECI_DYNAMIC_DISPLAY)
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: enableECIV2 ? graphqlOperation(
        isFromScanBoardingPass ? getDetailFlightForScanBoardingPassV2 : getDetailFlightV2,
        {
          direction,
          flightNumber,
          scheduledDate,
          flightStatus,
          myTravelUidEnabled,
          includeTravelInfo: true
        },
      ) : graphqlOperation(
        isFromScanBoardingPass ? getDetailFlightForScanBoardingPass : getDetailFlight,
        {
          direction,
          flightNumber,
          scheduledDate,
          airlineCode,
          flightStatus,
          myTravelUidEnabled,
          includeTravelInfo: true
        },
      ),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    const resData = !isEmpty(response?.data?.data) ? response?.data?.data : []
    if (!isFromScanBoardingPass && !resData?.getFlightDetails?.flightInfo) {
      throw "API Failed"
    }
    if (isFromScanBoardingPass && !resData?.getFlightDetailsForScanBoardingPass?.flightInfo) {
      throw "API Failed"
    }
    yield put(
      FlyCreators.flyFlightDetailsSuccess({
        data: formatResponseGetDetailFlight(resData, isFromScanBoardingPass, enableGTTD),
      }),
    )
  } catch (err) {
    console.log("errFlightDetails", err)
    if (err?.data?.getFlightDetail) {
      yield put(FlyCreators.flyFlightDetailsSuccess({ data: err?.data }, flightRequestType))
    } else {
      yield put(FlyCreators.flyFlightDetailsFailure(err.message, flightRequestType))
    }
  }
}
export function* getTimelineTilesQuery(action) {
  const { airline, destination, directionType, flight } = action

  try {
    const response = yield call(
      [API, "graphql"],
      graphqlOperation(getTravelChecklists, {
        input: { airline, destination, directionType, flight },
      }),
    )
    yield put(FlyCreators.flyTimelineTilesSuccess(response, env()))
  } catch (err) {
    yield put(FlyCreators.flyTimelineTilesFailure(err.message))
  }
}
export function* getIntoCityOrAirportQuery(action) {
  const { direction, flightUniqueId } = action
  const query = { direction, flightUniqueId }
  try {
    const response = yield call([API, "graphql"], graphqlOperation(getIntoCityOrAirport, query))
    yield put(FlyCreators.flyGetIntoCityOrAirportSuccess(response))
  } catch (err) {
    console.log("errGetIntoCityOrAirportQuery", err)
    yield put(FlyCreators.flyGetIntoCityOrAirportFailure(err.message))
  }
}

export const formatResponseMyTravelInsertFlight = (response) => {
  if (response?.data?.data?.saveFlight?.status === "success") {
    return {
      data: {
        insertMyTravelFlightDetail: {
          status: {
            errorCode: "",
            message: "Record saved successfully",
            status: null,
            statusCode: null,
            success: true,
          },
          referralFlow: response?.data?.data?.saveFlight?.referralFlow
        },
      },
    }
  }
  return response
}
export function* myTravelInsertFlightQuery(action) {
  const { input, payload } = action
  const isPassenger = !!input?.flightPax
  const connectingFlightPayload = store.getState()?.flyReducer?.connectingFlightPayload
  let connectingFlight = {}
  const dtAction = dtManualActionEvent(`${FE_LOG_PREFIX}App__flight-save-saga`)
  if (
    !isEmpty(connectingFlightPayload) &&
    connectingFlightPayload.isConnecting &&
    !isEmpty(connectingFlightPayload.flightConnecting)
  ) {
    connectingFlight = {
      flightDirection: connectingFlightPayload?.flightConnecting?.direction,
      flightNo: connectingFlightPayload?.flightConnecting?.flightNumber,
      iataAirportCode: connectingFlightPayload?.flightConnecting?.departingCode,
      scheduledDate: connectingFlightPayload?.flightConnecting?.flightDate,
      scheduledTime:
        connectingFlightPayload?.flightConnecting?.timeOfFlight ||
        connectingFlightPayload?.flightConnecting?.scheduledTime,
      terminal: connectingFlightPayload?.flightConnecting?.terminal,
    }
  }
  const promoCode = getEnvSetting().APPSCAPADE_PROMO_CODE
  const query = {
    input: {
      deviceId: DeviceInfo.getUniqueIdSync(),
      flightDirection: input?.flightDirection,
      ocidEmail: input?.enterpriseUserId,
      flightNo: input?.flightNumber,
      iataAirportCode:
        payload?.item?.direction === FlightDirection.departure
          ? payload?.item?.destinationCode
          : payload?.item?.departingCode,
      isPassenger,
      scheduledDate: payload?.item?.flightDate,
      scheduledTime: payload?.item?.timeOfFlight || payload?.item?.scheduledTime,
      terminal: payload?.item?.terminal,
      odtt: connectingFlightPayload?.isConnecting ? "TT" : "OD",
      ...(!isEmpty(connectingFlight) ? { connectingFlight: connectingFlight } : {}),
      ...(payload?.referrer ? {
        referrer: {
          uid: payload?.referrer,
          promoCode: promoCode
        }
      } : {}),
      flightStatus: payload?.item?.flightStatus
    },
  }
  dtAction.reportStringValue('flight-save-saga-flightNo', `${query.input.flightNo}`)
  dtAction.reportStringValue('flight-save-saga-scheduledDate', `${query.input.scheduledDate}`)
  dtAction.reportStringValue('flight-save-saga-flightDirection', `${query.input.flightDirection}`)
  dtAction.reportStringValue('flight-save-saga-isPassenger', String(query.input.isPassenger))
  try {
    if (payload?.flightNavigationType === FlightNavigationType.FlightAllSearch) {
      yield put(SearchActions.updateStatusFlight(payload))
    }
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(myTravelInsertFlight, query),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    if (payload?.flightNavigationType === FlightNavigationType.FlightAllSearch) {
      yield put(
        SearchActions.updateInsertFlightSearchAll(
          formatResponseMyTravelInsertFlight(response),
          payload,
        ),
      )
    }
    yield put(
      MytravelCreators.flyMyTravelInsertFlightSuccess(formatResponseMyTravelInsertFlight(response), { //payload - flightData
        ...payload,
        isPassenger,
      }),
    )
    dtAction.reportStringValue('flight-save-saga-success', "success")
      // get gamification reward game chance
      if(response?.data?.data?.saveFlight?.eligibleForGameChance){
        const { flightNumber, actualTimestamp, displayTimestamp, scheduledDate, scheduledTime, timeOfFlight } = payload?.item
        const priorityTime = actualTimestamp || displayTimestamp || `${scheduledDate} ${scheduledTime || timeOfFlight}`
        const formatedScheduledTime = momentTz(priorityTime).format("YYYY-MM-DD HH:mm").toString()
        const currentTimeToUTC = momentTz().tz("Asia/Singapore").format("YYYY-MM-DD HH:mm").toString()
        const gameChanceInput = {
          flightNumber: flightNumber,
          flightDatetime: formatedScheduledTime,
          saveTimestamp: currentTimeToUTC
        }
        yield call(restApi, {
          url: env()?.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: graphqlOperation(getGamificationGameChanceQuery, { input: gameChanceInput }),
          parameters: {},
          headers: {
            "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
          },
        })
      }
    
    if (input?.flightDirection === FlightDirection.arrival) {
      analyticsLogEvent(ANALYTICS_LOG_EVENT_NAME.SAVEFLIGHT_ARRIVAL)
      dtACtionLogEvent(ANALYTICS_LOG_EVENT_NAME.SAVEFLIGHT_ARRIVAL)
      AppEventsLogger.logEvent(FB_EVENT_NAME.SAVE_FLIGHT_ARRIVAL, null)
    } else if (input?.flightDirection === FlightDirection.departure) {
      analyticsLogEvent(ANALYTICS_LOG_EVENT_NAME.SAVEFLIGHT_DEPARTURE)
      dtACtionLogEvent(ANALYTICS_LOG_EVENT_NAME.SAVEFLIGHT_DEPARTURE)
      AppEventsLogger.logEvent(FB_EVENT_NAME.SAVE_FLIGHT_DEPARTURE, null)
    }
  } catch (err) {
    dtAction.reportStringValue('flight-save-saga-error', err?.message)
    if (payload?.flightNavigationType === FlightNavigationType.FlightAllSearch) {
      yield put(SearchActions.updateInsertRemoveFlightFailure(payload))
    }
    yield put(MytravelCreators.flyMyTravelInsertFlightFailure(payload))
  } finally {
    dtAction.leaveAction()
  }
}

export const formatResponseMyTravelFlight = (response) => {
  const dataSavedFlight = []
  isArray(response?.data?.getSavedFlights) &&
    forEach(response?.data?.getSavedFlights, (element) => {
      if (element?.flightInfo) {
        dataSavedFlight.push({
          logo:
            element?.flightInfo?.airline_details?.logo_url ||
            `https://d2xm3qu3nwgbrt.cloudfront.net/airlines/${element?.flightInfo?.flight_number.slice(
              0,
              2,
            )}.gif`,
          flightStatus: element?.flightInfo?.flight_status,
          flightStatusMapping: element?.flightInfo?.status_mapping?.listing_status_en,
          beltStatusMapping: element?.flightInfo?.status_mapping?.belt_status_en,
          statusColor: element?.flightInfo?.status_mapping?.status_text_color?.toLowerCase(),
          flightNumber: element?.flightInfo?.flight_number || element?.savedFlightInfo?.flightNo,
          codeShares: element?.flightInfo?.slave_flights,
          transits: [],
          scheduledDate:
            element?.flightInfo?.scheduled_date || element?.savedFlightInfo?.scheduledDate,
          scheduledTime:
            element?.flightInfo?.scheduled_time || element?.savedFlightInfo?.scheduledTime,
          departingCode: "",
          destinationCode: "",
          flightDirection:
            element?.flightInfo?.direction || element?.savedFlightInfo?.flightDirection,
          airport: element?.flightInfo?.airport,
          actualTimeStamp: element?.flightInfo?.actual_timestamp,
          estimatedTimestamp: element?.flightInfo?.estimated_timestamp,
          boardingGate: element?.flightInfo?.current_gate || element?.flightInfo?.display_gate,
          checkInRow: element?.flightInfo?.check_in_row,
          airportDetails: element?.flightInfo?.airport_details,
          terminal: element?.flightInfo?.terminal,
          displayBelt: element?.flightInfo?.display_belt,
          viaAirportDetails: element?.flightInfo?.via_airport_details,
          displayTimestamp: element?.flightInfo?.display_timestamp_mapping,
          upcomingStatusMapping: element?.flightInfo?.status_mapping?.details_status_en,
          technicalFlightStatus1: element?.flightInfo?.technical_flight_status1,
          technicalFlightStatus2: element?.flightInfo?.technical_flight_status2,
          isPassenger: element?.savedFlightInfo?.isPassenger === "True",
          displayGate: element?.flightInfo?.display_gate,
          showGate: element?.flightInfo?.status_mapping?.show_gate,
        })
      }
    })
  return {
    data: {
      getMyTravelFlightDetails: {
        success: true,
        message: "Record fetched successfully",
        data: dataSavedFlight,
      },
    },
  }
}
export function* getMyTravelFlightsQuery(action) {
  const dtAction = dtManualActionEvent(DT_ANALYTICS_LOG_EVENT_NAME.DT_GET_SAVED_FLIGHT)
  const { enterpriseUserId: email } = action
  const myTravelUidEnabled = true
  const startDate = moment().format(DateFormats.YearMonthDay)
  const endDate = moment().add(1, "year").format(DateFormats.YearMonthDay)
  const query = {
    startScheduledDate: startDate,
    endScheduledDate: endDate,
    username: email,
    myTravelUidEnabled,
  }
  try {
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(getMyTravelFlightDetails, query),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    dtAction.reportStringValue('status', 'success')
    yield put(MytravelCreators.flyMyTravelFlightsSuccess(formatResponseMyTravelFlight(response?.data)))
  } catch (err) {
    dtAction.reportStringValue('status', 'failed')
    yield put(MytravelCreators.flyMyTravelFlightsFailure(err.message))
  } finally {
    dtAction.leaveAction()
  }
}

export const formatResponseRemoveMyTravelFlight = (response) => {
  if (response?.data?.removeFlight?.status || response?.data?.removeFlight?.status === "success") {
    return {
      data: {
        deleteMyTravelFlightDetail: {
          status: {
            errorCode: "",
            message: "",
            status: true,
            statusCode: null,
            success: true,
          },
        },
      },
    }
  }
  return response
}

export function* myTravelRemoveFlightQuery(action) {
  const { payload } = action
  const query = {
    input: {
      flightDirection: payload?.item?.flightDirection || payload?.item?.direction,
      flightNo: payload?.item?.flightNumber,
      scheduledDate: payload?.item?.scheduledDate || payload?.item?.flightDate,
    },
  }
  const dtAction = dtManualActionEvent(`${FE_LOG_PREFIX}App__flight-unsave-saga`)
  dtAction.reportStringValue(
    "flight-unsave-saga-query-flightDirection",
    `${query.input.flightDirection}`,
  )
  dtAction.reportStringValue("flight-unsave-saga-query-flightNo", `${query.input.flightNo}`)
  dtAction.reportStringValue(
    "flight-unsave-saga-query-scheduledDate",
    `${query.input.scheduledDate}`,
  )
  try {
    if (payload?.flightNavigationType === FlightNavigationType.FlightAllSearch) {
      yield put(SearchActions.updateStatusFlight(payload))
    }
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(deleteMyTravelFlightDetail, query),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    if (payload?.flightNavigationType === FlightNavigationType.FlightAllSearch) {
      yield put(
        SearchActions.updateRemoveFlightSearchAll(
          formatResponseRemoveMyTravelFlight(response?.data),
          payload,
        ),
      )
    }
    yield put(
      MytravelCreators.flyMyTravelRemoveFlightSuccess(
        formatResponseRemoveMyTravelFlight(response?.data),
        payload,
      ),
    )
    dtAction.reportStringValue('flight-unsave-saga-success', "success")
  } catch (err) {
    dtAction.reportStringValue('flight-unsave-saga-error', err?.message)
    if (payload?.flightNavigationType === FlightNavigationType.FlightAllSearch) {
      yield put(SearchActions.updateInsertRemoveFlightFailure(payload))
    }
    yield put(MytravelCreators.flyMyTravelRemoveFlightFailure(payload))
  } finally {
    dtAction.leaveAction()
  }
}

export function* getFlightDetailsCrtQuery(action) {
  const { benefitType, flightDirection } = action
  const query = { benefitType, flightDirection }
  try {
    const response = yield call([API, "graphql"], graphqlOperation(getCRTBenefitCard, query))
    yield put(FlyCreators.flyFlightDetailsCrtSuccess(response))
  } catch (err) {
    yield put(FlyCreators.flyFlightDetailsCrtFailure(err.message))
  }
}

export function* getFlyConfig() {
  try {
    const response = yield call([API, "graphql"], graphqlOperation(getConfigQuery))
    yield put(FlyCreators.flyConfigSuccess(response))
  } catch (err) {
    yield put(FlyCreators.flyConfigFailure(err.errors))
  }
}

export function* getFlyContentShortLink() {
  try {
    const paramsArray = path.getFlyContentShortLink.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.AEM_URL + paramsArray[1]
    const response = yield call(restApi, { url, method, parameters: { location: "fly" } })
    if (!response.success || response.errors) {
      throw response
    }
    yield put(FlyCreators.flyContentShortLinkSuccess(response?.data?.list))
  } catch (error) {
    console.log("error", error)
    yield put(FlyCreators.flyContentShortLinkFailure(error))
  }
}

export function* getFlyContentDynamicBlock() {
  try {
    const paramsArray = path.getFlyLandingDynamicBlock.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.AEM_URL + paramsArray[1]
    const response = yield call(restApi, { url, method, parameters: { location: "fly" } })
    if (!response.success || response.errors) {
      throw response
    }
    yield put(FlyCreators.flyContentDynamicBlockSuccess(response?.data?.list))
  } catch (error) {
    console.log("error", error)
    yield put(FlyCreators.flyContentDynamicBlockFailure(error))
  }
}

export function* getDetailPerk() {
  try {
    const paramsArray = path.getFlightDetailPerks.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.AEM_URL + paramsArray[1]
    const response = yield call(restApi, { url, method })
    if (!response.success || response.errors) {
      throw response
    }
    yield put(FlyCreators.flightDetailPerkSuccess(response?.data?.list))
  } catch (error) {
    console.log("error", error)
    yield put(FlyCreators.flightDetailPerkFailure(error))
  }
}

export function* getFlyLandingPaginationDeparture(action: any) {
  const { direction, nextToken, filters } = action
  try {
    const response = yield* flyLandingQuery({ direction, nextToken, filters })
    const myTravelFlightsPayload = yield select((appState: RootState) => appState.mytravelReducer.myTravelFlightsPayload)
    yield put(FlyCreators.flyLandingPaginationDepartureSuccess(response, myTravelFlightsPayload))
  } catch (err) {
    yield put(FlyCreators.flyLandingPaginationDepartureFailure(err?.errors))
  }
}

export function* getFlyLandingPaginationArrival(action: any) {
  const { direction, nextToken, filters } = action
  try {
    const response = yield* flyLandingQuery({ direction, nextToken, filters })
    const myTravelFlightsPayload = yield select((appState: RootState) => appState.mytravelReducer.myTravelFlightsPayload)
    yield put(FlyCreators.flyLandingPaginationArrivalSuccess(response, myTravelFlightsPayload))
  } catch (err) {
    yield put(FlyCreators.flyLandingPaginationArrivalFailure(err?.errors))
  }
}

export function* getDepartureEarlierPaginationLanding(action) {
  const { direction, previousToken, filterDate, filters, isLoadFlightAfter24h } = action
  try {
    const responseEerlier = yield* flyEarlierListQuery({
      direction,
      prev: "true",
      previousToken,
      filterDate,
      filters,
      pageSize: "5",
      isLoadFlightAfter24h,
    })
    if (!isEmpty(responseEerlier?.data?.getFlights)) {
      const earlierDataFly = responseEerlier?.data?.getFlights
      const dataFly = {
        data: {
          getFlights: {
            flights: [...earlierDataFly?.flights?.reverse()],
            previous_token: earlierDataFly?.next_token,
          },
        },
      }
      const myTravelFlightsPayload = yield select((appState: RootState) => appState.mytravelReducer.myTravelFlightsPayload)
      yield put(FlyCreators.flyDeparturePaginationEarlierLandingSuccess(dataFly, myTravelFlightsPayload))
    } else throw responseEerlier
  } catch (err) {
    yield put(FlyCreators.flyDeparturePaginationEarlierLandingFailure(err?.message))
  }
}

export function* getArrivalEarlierPaginationLanding(action) {
  const { direction, previousToken, filterDate, filters, isLoadFlightAfter24h } = action
  try {
    const responseEerlier = yield* flyEarlierListQuery({
      direction,
      prev: "true",
      previousToken,
      filterDate,
      filters,
      pageSize: "5",
      isLoadFlightAfter24h,
    })
    if (!isEmpty(responseEerlier?.data?.getFlights)) {
      const earlierDataFly = responseEerlier?.data?.getFlights
      const dataFly = {
        data: {
          getFlights: {
            flights: [...earlierDataFly?.flights?.reverse()],
            previous_token: earlierDataFly?.next_token,
          },
        },
      }
      const myTravelFlightsPayload = yield select((appState: RootState) => appState.mytravelReducer.myTravelFlightsPayload)
      yield put(FlyCreators.flyArrivalPaginationEarlierLandingSuccess(dataFly, myTravelFlightsPayload))
    } else throw responseEerlier
  } catch (err) {
    yield put(FlyCreators.flyArrivalPaginationEarlierLandingFailure(err?.message))
  }
}

export async function checkFlightAvailable(action) {
  const { direction, flightNumber, scheduledDate, airlineCode, flightStatus } = action
  try {
    const myTravelUidEnabled = true
    const response = await restApi({
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(getDetailFlightForScanBoardingPass, {
        direction,
        flightNumber,
        scheduledDate,
        airlineCode,
        flightStatus,
        myTravelUidEnabled,
      }),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    return !isEmpty(response?.data?.data?.getFlightDetailsForScanBoardingPass?.flightInfo)
      ? response?.data?.data?.getFlightDetailsForScanBoardingPass?.flightInfo
      : []
  } catch (error) {
    console.log("errorGetDeepLink", error)
  }
}

export function* getAppscapadeBanner(action) {
  const { flightNumber, scheduledDate, direction } = action.input
  try {
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(getAppscapadeBannerQuery, {
        input: { flight_num: flightNumber, scheduled_date: scheduledDate, direction },
      }),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    const dataBanner = response?.data?.data?.getAppscapadeBanner
    if (dataBanner) {
      yield put(FlyCreators.getAppscapadeBannerSuccess(dataBanner))
    } else return response
  } catch (err) {
    yield put(FlyCreators.getAppscapadeBannerFailure(err))
  }
}
export async function checkLuckyDrawEligible(action) {
  const { flightNumber, scheduledDate, checkInSeqNumber, airportCode, direction } = action
  try {
    const response = await restApi({
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(getAppscapadeLuckyDrawEligibleQuery, {
        input: {
          flight_num: flightNumber,
          scheduled_date: scheduledDate,
          check_in_seq_num: checkInSeqNumber,
          airport_code: airportCode,
          direction: direction,
        },
      }),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    return !isEmpty(response?.data?.data?.getAppscapadeLuckyDrawEligible)
      ? response?.data?.data?.getAppscapadeLuckyDrawEligible
      : []
  } catch (error) {
    console.log("errorGetDeepLink", error)
  }
}

export function* searchDepartureFlightListingSaga(action) {
  const {
    direction,
    filterDate,
    filterTerminal,
    keyword,
    pageSize = 30,
    pageNumber = 1,
    isLoadMore = false,
  } = action
  try {
    let scheduledDate = moment(filterDate).format(DateFormats.YearMonthDay)
    const currentDate = moment().format(DateFormats.YearMonthDay)
    const isPast = new Date(scheduledDate) < new Date(currentDate)
    if (isPast) {
      scheduledDate = currentDate
    }
    const filterLocation = getQueryFilter(filterTerminal)
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(searchFlights, {
        text: keyword,
        filter: {
          direction,
          scheduled_date: scheduledDate,
          ...(filterLocation?.terminal?.length > 0 ? filterLocation : {}),
        },
        page_number: pageNumber,
        page_size: pageSize,
      }),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    const mappedResponse = {
      data: {
        getFlights: {
          flights: response.data.data.flights.items,
          page_number: response.data.data.flights.page_number,
          page_size: response.data.data.flights.page_size,
          total: response.data.data.flights.total,
        },
      },
    }
    const myTravelFlightsPayload = yield select((appState: RootState) => appState.mytravelReducer.myTravelFlightsPayload)
    yield put(FlyCreators.searchDepartureFlightListingSuccess(mappedResponse, isLoadMore, myTravelFlightsPayload))
  } catch (err) {
    // yield put(FlyCreators.searchDetailFlightsFailure({ error: err.message, query }))
    console.log(`TEST: searchDepartureFlightListing saga error ${err}`)
  }
}

export function* searchArrivalFlightListingSaga(action) {
  const {
    direction,
    filterDate,
    filterTerminal,
    keyword,
    pageSize = 30,
    pageNumber = 1,
    isLoadMore = false,
  } = action
  try {
    let scheduledDate = moment(filterDate).format(DateFormats.YearMonthDay)
    const currentDate = moment().format(DateFormats.YearMonthDay)
    const isPast = new Date(scheduledDate) < new Date(currentDate)
    if (isPast) {
      scheduledDate = currentDate
    }
    const filterLocation = getQueryFilter(filterTerminal)
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(searchFlights, {
        text: keyword,
        filter: {
          direction,
          scheduled_date: scheduledDate,
          ...(filterLocation?.terminal?.length > 0 ? filterLocation : {}),
        },
        page_number: pageNumber,
        page_size: pageSize,
      }),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    const mappedResponse = {
      data: {
        getFlights: {
          flights: response.data.data.flights.items,
          page_number: response.data.data.flights.page_number,
          page_size: response.data.data.flights.page_size,
          total: response.data.data.flights.total,
        },
      },
    }
    const myTravelFlightsPayload = yield select((appState: RootState) => appState.mytravelReducer.myTravelFlightsPayload)
    yield put(FlyCreators.searchArrivalFlightListingSuccess(mappedResponse, isLoadMore, myTravelFlightsPayload))
  } catch (err) {
    // yield put(FlyCreators.searchDetailFlightsFailure({ error: err.message, query }))
    console.log(`TEST: searchArrivalFlightListingSaga saga error ${err}`)
  }
}

export function* getFlightTickerBand() {
  const dtAction = dtManualActionEvent(DT_ANALYTICS_LOG_EVENT_NAME.DT_GET_FLY_TICKER_BAND)
  try {
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(getMaintenanceConfigurations),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    dtAction.reportStringValue('status', 'success')
    const maintenanceConfigurationsData = response?.data?.data?.getMaintenanceConfigurations
    const tickerBandData = maintenanceConfigurationsData?.find(
      (ele) => ele?.maintenanceType === MaintenanceType.TICKER_BAND && ele?.displayModule === DisplayModule.FLY,
    )
    const popUpData = maintenanceConfigurationsData?.find(
      (i) => i.maintenanceType === MaintenanceType.POP_UP,
    )
    if (popUpData) {
      dtAction.reportStringValue("popup", "true")
      yield put(PageConfigCreators.setMaintenancePopupData(popUpData))
    } else {
      dtAction.reportStringValue("popup", "false")
      yield put(PageConfigCreators.setMaintenancePopupData(null))
    }
    if (tickerBandData) {
      dtAction.reportStringValue("tickerBand", "true")
      yield put(FlyCreators.getFlightTickerBandSuccess(tickerBandData))
    } else {
      yield put(FlyCreators.getFlightTickerBandFailure())
    }
  } catch (err) {
    dtAction.reportStringValue("status", "failed")
    yield put(FlyCreators.getFlightTickerBandFailure())
  } finally {
    dtAction.leaveAction()
  }
}

export function* flySaga() {
  yield all([
    takeLatest(FlyTypes.FLY_DEPARTURE_LIST_REQUEST, getFlyDepartureList),
    takeLatest(FlyTypes.SEARCH_FLIGHTS_REQUEST, searchAllFlightsRequest),
    takeLatest(FlyTypes.SEARCH_DETAIL_FLIGHTS_REQUEST, searchDetailFlightsRequest),
    takeLatest(FlyTypes.FLY_DEPARTURE_PAGINATION_REQUEST, getDeparturePaginationResult),
    takeLatest(
      FlyTypes.FLY_DEPARTURE_PAGINATION_EARLIER_REQUEST,
      getDepartureEarlierPaginationResult,
    ),
    takeLatest(FlyTypes.FLY_LANDING_DEPARTURE_REQUEST, getFlyLandingDeparture),
    takeLatest(FlyTypes.FLY_LANDING_ARRIVAL_REQUEST, getFlyLandingArrival),
    takeLatest(FlyTypes.FLY_CITY_CODE_REQUEST, syncGetFlyCityCode),
    takeLatest(FlyTypes.FLY_ARRIVAL_LIST_REQUEST, getFlyArrivalList),
    takeLatest(FlyTypes.FLY_ARRIVAL_PAGINATION_REQUEST, getArrivalPaginationResult),
    takeLatest(FlyTypes.FLY_ARRIVAL_PAGINATION_EARLIER_REQUEST, getArrivalEarlierPaginationResult),
    takeLatest(FlyTypes.FLY_DEPARTURE_EARLIER_INITIAL_REQUEST, getDepartureEarlierInitialQuery),
    takeLatest(FlyTypes.FLY_ARRIVAL_EARLIER_INITIAL_REQUEST, getArrivalEarlierInitialQuery),
    takeLatest([FlyTypes.FLY_FLIGHT_DETAILS_REQUEST, FlyTypes.FLY_FLIGHT_DETAILS_REQUEST_FIRST], getFlightDetailsQuery),
    takeLatest(FlyTypes.FLY_LOCATION_FILTER_PARAMETERS_REQUEST, getFlyLocationFilterParameter),
    takeLatest(FlyTypes.FLY_GET_INTO_CITY_OR_AIRPORT_REQUEST, getIntoCityOrAirportQuery),
    takeLatest(FlyTypes.FLY_TIMELINE_TILES_REQUEST, getTimelineTilesQuery),
    takeLatest(MytravelTypes.FLY_MY_TRAVEL_INSERT_FLIGHT_REQUEST, myTravelInsertFlightQuery),
    takeLatest(MytravelTypes.FLY_MY_TRAVEL_FLIGHTS_REQUEST, getMyTravelFlightsQuery),
    takeLatest(MytravelTypes.FLY_MY_TRAVEL_REMOVE_FLIGHT_REQUEST, myTravelRemoveFlightQuery),
    takeLatest(FlyTypes.FLY_FLIGHT_DETAILS_CRT_REQUEST, getFlightDetailsCrtQuery),
    takeLatest(FlyTypes.FLY_CONFIG_REQUEST, getFlyConfig),
    takeLatest(FlyTypes.FLY_CONTENT_SHORT_LINK_REQUEST, getFlyContentShortLink),
    takeLatest(FlyTypes.FLY_CONTENT_DYNAMIC_BLOCK_REQUEST, getFlyContentDynamicBlock),
    takeLatest(FlyTypes.FLIGHT_DETAIL_PERK_REQUEST, getDetailPerk),
    takeLatest(FlyTypes.FLY_LANDING_PAGINATION_DEPARTURE_REQUEST, getFlyLandingPaginationDeparture),
    takeLatest(FlyTypes.FLY_LANDING_PAGINATION_ARRIVAL_REQUEST, getFlyLandingPaginationArrival),
    takeLatest(
      FlyTypes.FLY_DEPARTURE_PAGINATION_EARLIER_LANDING_REQUEST,
      getDepartureEarlierPaginationLanding,
    ),
    takeLatest(
      FlyTypes.FLY_ARRIVAL_PAGINATION_EARLIER_LANDING_REQUEST,
      getArrivalEarlierPaginationLanding,
    ),
    takeLatest(FlyTypes.GET_APPSCAPADE_BANNER_REQUEST, getAppscapadeBanner),
    takeLatest(FlyTypes.SEARCH_DEPARTURE_FLIGHT_LISTING, searchDepartureFlightListingSaga),
    takeLatest(FlyTypes.SEARCH_ARRIVAL_FLIGHT_LISTING, searchArrivalFlightListingSaga),
    takeLatest(FlyTypes.GET_FLIGHT_TICKER_BAND_REQUEST, getFlightTickerBand),
  ])
}
