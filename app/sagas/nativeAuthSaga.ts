import NativeAuthActions, { NativeAuthTypes } from "app/redux/nativeAuthRedux"
import { all, takeLatest, call, put, select } from "redux-saga/effects"
import path from "app/services/api/apis.json"
import { env } from "app/config/env-params"
import request from "app/services/api/request"
import ExploreCreators from "app/redux/exploreRedux"
import PageConfigAction from "app/redux/pageConfigRedux"
import ProfileActions, { ProfileSelectors } from "app/redux/profileRedux"
import NotificationAction from "app/redux/notificationRedux"
import DeviceInfo from "react-native-device-info"
import ForYouActions from "app/redux/forYouRedux"
import { API_FAILURE_KIND } from "app/utils/constants"
import { getAuthTokenPayload } from "app/utils/storage/mmkv-encryption-storage"
import { graphqlOperation } from "aws-amplify"
import { validatePromoCodeQuery } from "app/models/queries"
import NetInfo from "@react-native-community/netinfo"
import { MytravelCreators } from "app/redux/mytravelRedux"
import { convertStringValue, DT_ANALYTICS_LOG_EVENT_NAME, dtBizEvent, dtManualActionEvent } from "app/services/firebase"
import { getThrottleRegisterAttempt, setThrottleRegisterAttempt } from "app/utils/storage/mmkv-storage"
import moment from "moment"

export enum RES_CODE {
  NO_USER_ERROR = 403047,
  COMMON_SUCCESS = 200,
  INVALID_EMAIL_PASSWORD = 403042,
  DISABLED_ACCOUNT = 403041,
  ACCOUNT_EXIST = 403043,
  PENDING_VERIFY = 206006,
  LIMIT_REACHED = 400125,
  VERIFY_INVALID_PARAMS = 400006,
  ACCOUNT_LOCKED_OUT = 403120,
}
const checkNetwork = async () => await NetInfo.fetch()

const bizEventThrottleRegister = (email: string) => {
  const getThrottleRegister = getThrottleRegisterAttempt()
  const currentUtcDate = moment().utc().format()
  const newData = {
    email,
    date: currentUtcDate,
  }
  if (!getThrottleRegister || !getThrottleRegister?.length) {
    setThrottleRegisterAttempt([newData])
  } else {
    if (getThrottleRegister?.length >= 3) {
      const firstData = getThrottleRegister[0]
      if (moment(firstData?.date).add(24, "hours").isAfter(moment(currentUtcDate))) {
        dtBizEvent("RegisterScreen", "RegisterRequest", "dt_throttle_registration", {
          email: email,
          date: currentUtcDate,
        })
        getThrottleRegister.shift()
        setThrottleRegisterAttempt([...getThrottleRegister, newData])
      } else {
        getThrottleRegister.shift()
        setThrottleRegisterAttempt([...getThrottleRegister, newData])
      }
    } else {
      setThrottleRegisterAttempt([...getThrottleRegister, newData]) 
    }
  }
}

export function* handleSendEmailResetRequest(action) {
  try {
    const { email, sourceSystem } = action
    const data = { email, source_system: sourceSystem }
    const paramsArray = path.sendResetEmail.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.MW_AUTH_URL + paramsArray[1]
    const headers = {
      "x-api-key": env()?.MW_AUTH_API_KEY,
    }
    const response = yield call(request, {
      url,
      method,
      data,
      headers,
    })
    if (!response.success && response.error) {
      throw response
    }
    yield put(NativeAuthActions.sendEmailResetSuccess(response?.statusCode))
  } catch (error) {
    yield put(
      NativeAuthActions.sendEmailResetFailure({
        status: error?.data?.error_code || error.errorCode || error.statusCode,
      }),
    )
  }
}

export function* handleSubmitMissingData(action) {
  try {
    const { data } = action
    const { first_name } = data || {}
    const paramsArray = path.submitMissingData.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.MW_AUTH_URL + paramsArray[1]
    const headers = {
      "x-api-key": env()?.MW_AUTH_API_KEY,
    }
    const response = yield call(request, {
      url,
      method,
      data,
      headers,
    })
    if (!response.success && response.error) {
      throw response
    }
    if (response?.data?.error_code) {
      throw response
    }
    yield put(
      NativeAuthActions.submitMissingDataSuccess({
        firstName: first_name || "",
        status: response?.statusCode,
        res: response?.data,
      }),
    )
  } catch (error) {
    yield put(NativeAuthActions.submitMissingDataFailure({ status: error?.statusCode }))
  }
}

export function* handleValidatePromoCode(action) {
  try {
    const { data } = action
    const { promoInput = {}, referralInput = {} } = data
    const response = yield call(request, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(validatePromoCodeQuery, { promoInput, referralInput }),
    })
    if (!response.success && response.error) {
      throw response
    }
    if (response?.data?.error_code) {
      throw response
    }
    yield put(NativeAuthActions.validatePromoCodeSuccess(response?.data?.data))
  } catch (error) {
    yield put(
      NativeAuthActions.validatePromoCodeFailure({
        status: error?.data?.error_code || error.errorCode || error.statusCode,
      }),
    )
  }
}

export function* handleLoginRequest(action) {
  try {
    const { email, sourceSystem, password, captchaToken } = action
    const data = {
      username: email,
      source_system: sourceSystem,
      password,
      ...(captchaToken && { captcha_token: captchaToken }),
    }
    const paramsArray = path.sendLoginRequest.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.MW_AUTH_URL + paramsArray[1]
    const headers = {
      "x-api-key": env()?.MW_AUTH_API_KEY,
    }
    const response = yield call(request, {
      url,
      method,
      data,
      headers,
    })
    if (!response.success && response.error) {
      throw response
    }
    if (response?.data?.error_code) {
      throw response
    }
    if (response?.data?.signup?.message?.missing_fields) {
      yield put(NativeAuthActions.loginMissingField(response?.statusCode, response?.data))
    } else yield put(NativeAuthActions.loginSuccess(response?.statusCode, response?.data))
  } catch (error) {
    yield put(
      NativeAuthActions.loginFailure({
        status: error?.data?.error_code || error.errorCode || error.statusCode,
        email: action.email,
      }),
    )
  }
}

export function* nativeAuthTokenVerifyRequest() {
  const dtAction = dtManualActionEvent(DT_ANALYTICS_LOG_EVENT_NAME.DT_POST_VALIDATE_TOKEN)
  try {
    const authTokenPayload = yield call(getAuthTokenPayload)
    const token = authTokenPayload?.postMethod?.access_token
    let isForce = false
    if (!token) {
      isForce = true
    }
    if (isForce) {
      dtAction.reportStringValue("status", "no-token")
      const deviceId = DeviceInfo.getUniqueIdSync()
      yield put(NativeAuthActions.authLogout())
      yield put(ExploreCreators.getUpcomingEventReset())
      yield put(PageConfigAction.setClearCacheProfileTimestamp(0))
      yield put(PageConfigAction.setClearCacheTransactionTimestamp(0))
      yield put(ProfileActions.putUserDeviceTokenRequest())
      yield put(ProfileActions.profilePayloadReset())
      yield put(NotificationAction.notificationsCountRequest(deviceId))
      yield put(MytravelCreators.resetMyTravelFlights()) // Don't remove it
      yield put(ForYouActions.rewardsReset())
      return null
    }

    if (authTokenPayload?.kind !== "ok" || !token) {
      dtAction.reportStringValue("status", "not-ok")
      throw authTokenPayload
    }
    const profilePayload = yield select(ProfileSelectors.profilePayload)
    const paramsArray = path.postValidateToken.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.API_GATEWAY_URL + paramsArray[1]
    const response = yield call(request, {
      url,
      method,
      headers: {
        uid: profilePayload?.id,
      },
    })
    if (response.success) {
      dtAction.reportStringValue("status", "success")
      yield put(NativeAuthActions.nativeAuthTokenVerifySuccess(response.data))
    } else {
      throw response
    }
  } catch (error) {
    dtAction.reportStringValue("status", "failed")
    dtAction.reportStringValue("statusCode", `${error?.statusCode}`)
    if (error?.statusCode && error?.statusCode === 403) {
      yield put(NativeAuthActions.nativeAuthTokenVerifyFailure(API_FAILURE_KIND.UNAUTHORIZED))
    } else {
      yield put(NativeAuthActions.nativeAuthTokenVerifyFailure(API_FAILURE_KIND.CANNOT_CONNECT))
    }
  } finally {
    dtAction.leaveAction()
  }
}

export function* handleSignUpRequest(action) {
  const dtAction = dtManualActionEvent(DT_ANALYTICS_LOG_EVENT_NAME.DT_POST_REGISTER)
  const { email, sourceSystem, password } = action
  try {
    const paramsArray = path.sendSignUpRequest.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.MW_AUTH_URL + paramsArray[1]
    const data = { username: email, source_system: sourceSystem, password }
    const headers = {
      "x-api-key": env()?.MW_AUTH_API_KEY,
    }
    dtAction.reportStringValue("email", email)
    dtAction.reportStringValue("sourceSystem", sourceSystem)
    const response = yield call(request, {
      url,
      method,
      data,
      headers,
    })
    if (!response.success && response.error) {
      dtAction.reportStringValue("status", "error")
      throw response
    }
    if (!!response?.statusCode && response?.statusCode === 200) {
      bizEventThrottleRegister(email)
    }
    dtAction.reportStringValue("status", "success")
    dtAction.reportStringValue("statusCode", convertStringValue(response?.statusCode))
    yield put(
      NativeAuthActions.signUpSuccess({ status: response?.statusCode, res: response?.data }),
    )
    yield put(NativeAuthActions.clearSendOtpCodeStatus())
    const network = yield call(checkNetwork)
    if (network.isConnected) yield put(NativeAuthActions.sendOtpCode(email, sourceSystem))
    else yield put(NativeAuthActions.setConnectionStatus(false))
  } catch (error) {
    bizEventThrottleRegister(email)
    dtAction.reportStringValue("status", "failed")
    dtAction.reportStringValue("errorCode", convertStringValue(error?.errorCode))
    dtAction.reportStringValue("statusCode", convertStringValue(error?.statusCode))
    yield put(
      NativeAuthActions.signUpFailure({
        status: error.errorCode || error.statusCode,
      }),
    )
  } finally {
    dtAction.leaveAction()
  }
}

export function* handleSendOTPCode(action) {
  try {
    const { email, sourceSystem } = action

    const paramsArray = path.sendOTPCode.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.MW_AUTH_URL + paramsArray[1]
    const data = { username: email, source_system: sourceSystem }
    const headers = {
      "x-api-key": env()?.MW_AUTH_API_KEY,
    }
    const response = yield call(request, {
      url,
      method,
      data,
      headers,
    })
    if (!response.success && response.error) {
      throw response
    }
    yield put(
      NativeAuthActions.sendOtpCodeSuccess({
        status: response?.statusCode,
        vtoken: response?.data?.vtoken,
      }),
    )
  } catch (error) {
    yield put(
      NativeAuthActions.sendOtpCodeFailure({
        status: error.errorCode || error.statusCode,
      }),
    )
  }
}

export function* handleVerifyOTPCode(action) {
  try {
    const { email, sourceSystem, code, vtoken } = action

    const paramsArray = path.verifyOTPCode.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.MW_AUTH_URL + paramsArray[1]
    const data = { username: email, source_system: sourceSystem, code, vtoken }
    const headers = {
      "x-api-key": env()?.MW_AUTH_API_KEY,
    }
    const response = yield call(request, {
      url,
      method,
      data,
      headers,
    })
    if (!response.success && response.error) {
      throw response
    }
    yield put(NativeAuthActions.verifyOtpCodeSuccess({ status: response?.statusCode, data: response?.data }))
  } catch (error) {
    yield put(
      NativeAuthActions.verifyOtpCodeFailure({
        status: error.errorCode || error.statusCode,
      }),
    )
  }
}

export function* handleGetAccountInfoRequest(action) {
  try {
    const { uid, accessToken, expiresIn } = action
    const paramsArray = path.getAccountInfo.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.MW_AUTH_URL + paramsArray[1]
    const data = { uid: uid, access_token: accessToken }
    const headers = {
      "x-api-key": env()?.MW_AUTH_API_KEY,
    }
    const response = yield call(request, {
      url,
      method,
      data,
      headers,
    })
    if (!response.success && response.error_code) {
      throw response
    }
    if (response?.success && response?.data?.message) {
      yield put(NativeAuthActions.getAccountInfoSuccess(response.data?.message))
      yield put(NativeAuthActions.getFieldsRequest(accessToken, uid))
      return
    }
  } catch (error) {
    yield put(
      NativeAuthActions.getAccountInfoFailure({
        ...error,
        status: error.errorCode || error.statusCode,
      }),
    )
  }
}

export function* handleGetFieldsRequest(action) {
  try {
    const { accessToken, uid } = action
    const paramsArray = path.getFieldsAccount.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.MW_AUTH_URL + paramsArray[1]
    const data = { access_token: accessToken }
    const headers = {
      "x-api-key": env()?.MW_AUTH_API_KEY,
    }
    const response = yield call(request, {
      url,
      method,
      data,
      headers,
    })
    if (!response.success && response.error) {
      throw response
    }
    yield put(
      NativeAuthActions.getFieldsSuccess(
        { status: response?.statusCode, res: response?.data },
        uid,
      ),
    )
  } catch (error) {
    yield put(
      NativeAuthActions.getFieldsFailure({
        status: error.errorCode || error.statusCode,
      }),
    )
  }
}

function* handleVerifyCredentials(action) {
  try {
    const { email, password } = action
    const authTokenPayload = yield call(getAuthTokenPayload)
    const token = authTokenPayload?.postMethod?.access_token
    const paramsArray = path.verifyCredentials.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.MW_AUTH_URL + paramsArray[1]
    const data = { password, username: email }
    const headers = {
      "x-api-key": env()?.MW_AUTH_API_KEY,
      token,
    }
    const response = yield call(request, {
      url,
      method,
      data,
      headers,
    })    
    if (!response.success && response.error) {
      throw response
    }
    if (response?.data?.error_code) {
      throw response
    }
    yield put(NativeAuthActions.verifyCredentialsSuccess({ status: response?.statusCode }))
  } catch (error) {
    yield put(
      NativeAuthActions.verifyCredentialsFailure({ status: error.errorCode || error.statusCode }),
    )
  }
}

export function* handleLinkAccount(action) {
  try {
    const { email, password, regToken } = action
    const paramsArray = path.linkAccount.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.MW_AUTH_URL + paramsArray[1]
    const data = { password, email, reg_token: regToken }
    const headers = {
      "x-api-key": env()?.MW_AUTH_API_KEY,
    }
    const response = yield call(request, {
      url,
      method,
      data,
      headers,
    })
    if (!response.success && response.error) {
      throw response
    }
    if (response?.data?.error_code) {
      throw response
    }
    yield put(
      NativeAuthActions.linkAccountSuccess({ status: response?.statusCode, res: response?.data }),
    )
  } catch (error) {
    yield put(
      NativeAuthActions.linkAccountFailure({
        status: error?.data?.error_code || error?.errorCode || error?.statusCode,
      }),
    )
  }
}

export function* handleDelinkAccount(action) {
  try {
    const { uid, provider } = action
    const paramsArray = path.delinkAccount.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.MW_AUTH_URL + paramsArray[1]
    const data = { uid, provider }
    const headers = {
      "x-api-key": env()?.MW_AUTH_API_KEY,
    }
    const response = yield call(request, {
      url,
      method,
      data,
      headers,
    })
    if (!response.success && response.error) {
      throw response
    }
    if (response?.data?.error_code) {
      throw response
    }
    yield put(NativeAuthActions.delinkAccountSuccess())
  } catch (error) {
    yield put(NativeAuthActions.delinkAccountFailure())
  }
}

export function* nativeAuthSaga() {
  yield all([
    takeLatest(NativeAuthTypes.SEND_EMAIL_RESET_REQUEST, handleSendEmailResetRequest),
    takeLatest(NativeAuthTypes.NATIVE_AUTH_TOKEN_VERIFY_REQUEST, nativeAuthTokenVerifyRequest),
    takeLatest(NativeAuthTypes.SEND_LOGIN_REQUEST, handleLoginRequest),
    takeLatest(NativeAuthTypes.SEND_SIGN_UP_REQUEST, handleSignUpRequest),
    takeLatest(NativeAuthTypes.SEND_OTP_CODE, handleSendOTPCode),
    takeLatest(NativeAuthTypes.VERIFY_OTP_CODE, handleVerifyOTPCode),
    takeLatest(NativeAuthTypes.SUBMIT_MISSING_DATA, handleSubmitMissingData),
    takeLatest(NativeAuthTypes.VALIDATE_PROMO_CODE, handleValidatePromoCode),
    takeLatest(NativeAuthTypes.GET_ACCOUNT_INFO_REQUEST, handleGetAccountInfoRequest),
    takeLatest(NativeAuthTypes.GET_FIELDS_REQUEST, handleGetFieldsRequest),
    takeLatest(NativeAuthTypes.LINK_ACCOUNT, handleLinkAccount),
    takeLatest(NativeAuthTypes.DELINK_ACCOUNT, handleDelinkAccount),
    takeLatest(NativeAuthTypes.VERIFY_CREDENTIALS, handleVerifyCredentials),
  ])
}
