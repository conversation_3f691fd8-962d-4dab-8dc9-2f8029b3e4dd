import { Platform } from "react-native"
import { all, put, call, select, takeLatest, delay } from "redux-saga/effects"
import { API, graphqlOperation } from "aws-amplify"
import { get } from "lodash"
import * as storage from "app/utils/storage"
import ProfileAction, { ProfileTypes, ProfileSelectors } from "app/redux/profileRedux"
import PageConfigAction from "app/redux/pageConfigRedux"
import {
  getSubscriptionCentresQuery,
  updateSubscriptionCentresQuery,
  getSettingUserPreferences,
  putUserPreferences,
  linkCapitastarMobileNoQuery,
  sendOtp,
  verifyOtp,
  putUserPreferencesV2,
  getSettingUserPreferencesV2,
} from "app/models/queries"
import { putUserDeviceTokenMutation } from "app/models/mutations"
import DeviceInfo from "react-native-device-info"
import { env } from "app/config/env-params"
import restApi from "app/services/api/request"
import isEmpty from "lodash/isEmpty"
import reduce from "lodash/reduce"
import { convertStringValue, DT_ANALYTICS_LOG_EVENT_NAME, dtManualActionEvent, getDeviceToken } from "app/services/firebase"
import { store } from "app/redux/store"
import { StorageKey, loadFromEncryptedStorage } from "app/utils/storage"
import path from "app/services/api/apis.json"
import { SettingNotificationID } from "app/models/enum"
import { handleCondition } from "app/utils"
import { GET_ACCOUNT_PROFILE_RETRY_ATTEMPTS_DEFAULT } from "app/utils/constants"
import Braze from "@braze/react-native-sdk"

const deviceId = DeviceInfo.getUniqueIdSync()
const deviceName = DeviceInfo.getDeviceNameSync()

const initListConfig = [
  {
    subscription_group_id: "FLIGHT_STATUS_UPDATE",
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: "FLIGHT_CONFIRMED",
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: "FLIGHT_BOARDING",
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: "FLIGHT_GATE_CLOSTING",
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: "FLIGHT_LANDED",
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: "BAGGAGE_INFO",
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: "EVENT_SHOPPING_DEALS",
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: "EVENT_FANDB_PROMOTIONS",
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: "EVENT_LATEST_EVENTS",
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: "EVENT_CHANGI_REWARDS_MEMBER_EXCLUSIVES",
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: "EVENT_CHANGI_REWARDS",
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: "EVENT_CHANGI_PAY",
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: "EVENT_ISHOP_CHANGI",
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: "EVENT_CHANGI_EATS",
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: "EVENT_STAY_VILIGANT",
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: "EVENT_CHANGI_AIRPORT",
    subscription_status: false,
    subscription_type: "EMAIL",
  },
  {
    subscription_group_id: "EVENT_ADVISORY",
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: SettingNotificationID.EVENT_REMINDER,
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: SettingNotificationID.EVENT_PERKS,
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: SettingNotificationID.EVENT_PP_CREDITS,
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: SettingNotificationID.FLIGHT_AND_BAGGAGE_ALERTS,
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: SettingNotificationID.EVENTS_AND_PROMOTIONS_MARKETING,
    subscription_status: false,
    subscription_type: "INAPP",
  },
  {
    subscription_group_id: SettingNotificationID.TRANSACTIONS_AND_MORE,
    subscription_status: false,
    subscription_type: "INAPP",
  },
]

export function* getAccountProfileWithRetry(attempts: number) {
  const dtAction = dtManualActionEvent(DT_ANALYTICS_LOG_EVENT_NAME.DT_PUT_USER_DEVICE_TOKEN)
  if(attempts <= 0){
    yield put(ProfileAction.profileFailure({error: "error"}))
    dtAction.reportStringValue("status", "failed-reach-max-attempts")
    dtAction.leaveAction()
    return;
  }
  try {
    const timeStamp = new Date().getTime()
    const paramsArray = path.getAccountProfile.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.API_GATEWAY_URL + paramsArray[1]
    const result = yield call(restApi, {
      url,
      method,
    })
    const { data, errors } = result
    if (errors) {
      throw errors
    }
    if (!data?.status?.success) {
      throw data?.status
    }
    if (data?.status?.status !== "success") {
      throw data?.data?.crStatus
    }
    if(data?.status?.status === "success" && isEmpty(data?.data?.cardNo)){
      const remainingAttempts = attempts - 1
      if(remainingAttempts <= 0){
        dtAction.reportStringValue('status', 'success')
        yield put(ProfileAction.profileSuccess(data))
        return
      } else {
        yield delay(3000)
        dtAction.reportStringValue('attempts', `${attempts}`)
        dtAction.reportStringValue('status', 'try-again')
        yield call(getAccountProfileWithRetry, remainingAttempts)
        return
      }
    }
    yield put(ProfileAction.profileSuccess(data))
    yield put(PageConfigAction.setClearCacheProfileTimestamp(timeStamp))
  } catch (error){
    dtAction.reportStringValue('status', 'failed')
    yield put(ProfileAction.profileFailure(error))
  } finally {
    dtAction.leaveAction()
  }
}

export function* getProfile() {
  try {
    const profileData = store.getState().profileReducer.profilePayload
    const timeStamp = new Date().getTime()
    const cacheTimeStamp = store.getState().pageConfigReducer.clearCacheProfileTimeStamp
    if (
      !profileData ||
      !cacheTimeStamp ||
      cacheTimeStamp + env()?.CACHE_PROFILE_INTERVAL < timeStamp
    ) {
      const numberOfTryConfig = yield call(getAppConfigurationsSaga, {configKey: "cr_no_tries"})
      let retryAttempt = GET_ACCOUNT_PROFILE_RETRY_ATTEMPTS_DEFAULT
      try {
        retryAttempt = numberOfTryConfig ? Number(numberOfTryConfig) : GET_ACCOUNT_PROFILE_RETRY_ATTEMPTS_DEFAULT
      } catch (error){
        retryAttempt = GET_ACCOUNT_PROFILE_RETRY_ATTEMPTS_DEFAULT
      }
      yield call(getAccountProfileWithRetry, retryAttempt)
    } else {
      const storeData = {
        status: {
          success: true,
          message: "",
          errorCode: "",
          status: "success",
        },
        data: profileData,
      }
      yield put(ProfileAction.profileSuccess(storeData))
    }
  } catch (error) {
    yield put(ProfileAction.profileFailure(error))
  }
}

export function* updateProfile(action) {
  const { payload } = action
  try {
    const profile = yield select(ProfileSelectors.profilePayload)

    const data = {
      userName: profile?.email,
      ...payload,
    }

    const paramsArray = path.updateAccountProfile.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.API_GATEWAY_URL + paramsArray[1]
    const response = yield call(restApi, {
      url,
      method,
      data,
    })

    if (response?.success && response?.data?.status?.success) {
      yield put(ProfileAction.profileUpdateSuccess(response?.data))
      yield put(ProfileAction.updateProfileData(payload))
      return
    }

    throw response?.data
  } catch (error) {
    yield put(ProfileAction.profileUpdateFailure(error))
  }
}

export function* linkMembership(action) {
  try {
    const { mobileNumber, memberId } = action

    const query = {
      mobileNumber: mobileNumber,
      memberId: memberId,
    }

    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(linkCapitastarMobileNoQuery, query),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    const responseData = response?.data?.data
    if (!responseData?.errors && responseData?.linkCapitastarMobileNo?.status?.success) {
      yield put(ProfileAction.linkMembershipSuccess(mobileNumber))
    } else {
      const error = {
        aemEHRCode: responseData?.linkCapitastarMobileNo?.status?.aemEHRCode,
        errorCode: responseData?.linkCapitastarMobileNo?.status?.errorCode,
      }
      yield put(ProfileAction.linkMembershipFailure(error))
    }
  } catch (error) {
    const errorObj = {
      aemEHRCode: 'EHR3.10',
      errorCode: '',
    }
    yield put(ProfileAction.linkMembershipFailure(errorObj))
  }
}

export function* getChangePasswordUrl() {
  try {
    const paramsArray = path.getResetPasswordUrl.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.API_GATEWAY_URL + paramsArray[1]
    const res = yield call(restApi, {
      url,
      method,
    })
    yield put(ProfileAction.changePasswordUrlSuccess(res))
  } catch (error) {
    yield put(ProfileAction.changePasswordUrlFailure())
  }
}

export function* getSubscription() {
  try {
    const profile = yield select(ProfileSelectors.profilePayload)
    const {
      data: { getSubscriptionCentres },
      errors,
    } = yield call(
      [API, "graphql"],
      graphqlOperation(getSubscriptionCentresQuery, {
        username: profile?.email,
      }),
    )

    if (errors) {
      throw errors
    }
    yield put(ProfileAction.subscriptionSuccess(getSubscriptionCentres))
  } catch (error) {
    yield put(ProfileAction.subscriptionFailure())
  }
}

export function* updateSubscription(action) {
  const { formData, formType } = action

  try {
    const profile = yield select(ProfileSelectors.profilePayload)

    const {
      data: { updateSubscriptionCentres },
    } = yield call(
      [API, "graphql"],
      graphqlOperation(updateSubscriptionCentresQuery, {
        username: profile?.email,
        input: {
          id: profile?.id,
          ...formData,
        },
      }),
    )

    if (updateSubscriptionCentres?.success) {
      yield put(ProfileAction.subscriptionUpdateSuccess(formData, formType))
      return
    }

    yield put(ProfileAction.subscriptionUpdateFailure(formType))
  } catch (error) {
    yield put(ProfileAction.subscriptionUpdateFailure(formType))
  }
}

export function* getDeletePageContent() {
  try {
    yield put(ProfileAction.fetchDeletePageContentLoading())
    const paramsArray = path.getAccountAemContent.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.API_GATEWAY_URL + paramsArray[1]
    const response = yield call(restApi, {
      url,
      method,
      parameters: {
        type: "delete_account_content",
      },
    })
    const deletePageContent = get(response, "data", {})
    yield put(ProfileAction.getDeletePageContentSuccess(deletePageContent))
  } catch (error) {
    yield put(
      ProfileAction.getDeletePageContentFailure({
        error: error,
      }),
    )
  }
}

export function* getProfileContentAem() {
  try {
    const paramsArray = path.getAccountAemContent.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.API_GATEWAY_URL + paramsArray[1]
    const response = yield call(restApi, {
      url,
      method,
      parameters: {
        type: "informative_text",
      },
    })
    const profileContent = get(response, "data", {})
    yield put(ProfileAction.profileContentAemSuccess(profileContent))
  } catch (error) {
    yield put(
      ProfileAction.profileContentAemFailure({
        error,
      }),
    )
  }
}

export function* updateLegacyUserRequest(action) {
  const { payload } = action
  try {
    const paramsArray = path.updateUserDetails.split(" ")
    const method = paramsArray[0] || "GET"
    const url = env()?.API_GATEWAY_URL + paramsArray[1]
    const response = yield call(restApi, {
      url,
      method,
      data: payload,
    })
    if (response?.data?.statusCode !== 500) {
      yield put(ProfileAction.profileUpdateLegacyUserStatus({ isSuccess: true, message: "" }))
    } else throw response?.data
  } catch (error) {
    yield put(
      ProfileAction.profileUpdateLegacyUserStatus({
        isSuccess: false,
        messageError: JSON.parse(error?.body),
      }),
    )
  }
}

const mappingSettingToObject = (data) => {
  const listConfig = initListConfig.map((item) => {
    const settingData = data?.find?.(
      (setting) => setting?.subscription_group_id === item?.subscription_group_id,
    )
    return settingData || item
  })

  const dataResult = reduce(
    listConfig,
    function (result, value) {
      if (!result[value?.subscription_group_id]) {
        result[value?.subscription_group_id] = !!value?.subscription_status
      }
      return result
    },
    {},
  )
  return dataResult
}

const getLastLoginUserAsync = async () =>
  await loadFromEncryptedStorage(StorageKey.lastLoggedInUser)

const getBrazeDeviceIdAsync = (): Promise<string> {
  return new Promise((resolve) => {
    Braze.getDeviceId((error, result) => {
      resolve(result)
    })
  })
}


export function* settingNotificationRequest(action) {
  const { params = {} } = action
  const { isNotificationPreferencesV2 } = params
  const DEVICE_ID = DeviceInfo.getUniqueIdSync()
  const profileData = store.getState().profileReducer.profilePayload
  try {
    const LAST_LOGGED_IN_USER = yield call(getLastLoginUserAsync)
    const query = {
      last_login_id: profileData?.id || LAST_LOGGED_IN_USER?.uid || null,
      email: profileData?.email || LAST_LOGGED_IN_USER?.email || null,
      device_id: DEVICE_ID,
      category: "INAPP",
      subscription_group_ids: [],
      braze_id: ,
    }
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(
        isNotificationPreferencesV2 ? getSettingUserPreferencesV2 : getSettingUserPreferences,
        query,
      ),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    const resData = handleCondition(
      isNotificationPreferencesV2,
      response?.data?.data?.getUserPreferences_v2,
      response?.data?.data?.getUserPreferences,
    )
    if (isEmpty(response?.data?.errors) && response?.success && !!resData) {
      yield put(
        ProfileAction.settingNotificationSuccess(
          mappingSettingToObject(resData),
        ),
      )
      return
    } else {
      yield put(ProfileAction.settingNotificationFailure())
    }
  } catch (error) {
    yield put(ProfileAction.settingNotificationFailure())
  }
}

export function* settingNotificationEmailSmsRequest() {
  try {
    const query = {
      device_id: DeviceInfo.getUniqueIdSync(),
      category: "EMAIL_SMS",
      subscription_group_ids: [
        "EVENT_CHANGI_REWARDS",
        "EVENT_ISHOP_CHANGI",
        "EVENT_CHANGI_AIRPORT",
      ],
    }
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(getSettingUserPreferences, query),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    if (isEmpty(response?.data?.errors) && response?.success) {
      yield put(
        ProfileAction.settingNotificationEmailSmsSuccess(response?.data?.data?.getUserPreferences),
      )
      return
    } else {
      yield put(ProfileAction.settingNotificationEmailSmsFailure())
    }
  } catch (error) {
    yield put(ProfileAction.settingNotificationEmailSmsFailure())
  }
}

export function* changeSettingNotificationRequest(action) {
  const { options = {}, params, query: argumentQuery } = action
  const { isNotificationPreferencesV2 } = options
  const DEVICE_ID = DeviceInfo.getUniqueIdSync()
  const profileData = store.getState().profileReducer.profilePayload
  try {
    const LAST_LOGGED_IN_USER = yield call(getLastLoginUserAsync)
    const query = {
      device_id: DEVICE_ID,
      last_login_id: profileData?.id || LAST_LOGGED_IN_USER?.uid || null,
      subscription_groups: params,
      language: params?.language ? params?.language : "en",
      email: profileData?.email || LAST_LOGGED_IN_USER?.email || null,
      phone_number: `+${profileData?.countryCode}${profileData?.phoneNum}`,
      ...argumentQuery,
    }
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(
        isNotificationPreferencesV2 ? putUserPreferencesV2 : putUserPreferences,
        query,
      ),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    const resData = handleCondition(
      isNotificationPreferencesV2,
      response?.data?.data?.putUserPreferences_v2,
      response?.data?.data?.putUserPreferences,
    )
    if (resData?.status === "success") {
      yield put(ProfileAction.changeSettingNotificationSuccess(params))
      return
    }
    yield put(ProfileAction.changeSettingNotificationFailure(params))
  } catch (error) {
    yield put(ProfileAction.changeSettingNotificationFailure(params))
  }
}

export function* changeSettingNotificationEmailSmsRequest(action) {
  const { params, language } = action
  const profileData = store.getState().profileReducer.profilePayload
  try {
    const LAST_LOGGED_IN_USER = yield call(getLastLoginUserAsync)
    const query = {
      device_id: DeviceInfo.getUniqueIdSync(),
      subscription_groups: params,
      language: language || "en",
      email: profileData?.email,
      phone_number: `+${profileData?.countryCode}${profileData?.phoneNum}`,
      last_login_id: profileData?.id || LAST_LOGGED_IN_USER?.uid || null,
    }
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(putUserPreferences, query),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    if (response?.data?.data?.putUserPreferences?.status === "success") {
      yield put(ProfileAction.changeSettingNotificationEmailSmsSuccess(params, language))
      return
    }
    yield put(ProfileAction.changeSettingNotificationEmailSmsFailure(params))
  } catch (error) {
    yield put(ProfileAction.changeSettingNotificationEmailSmsFailure(params))
  }
}

export function* putUserDeviceTokenRequest() {
  const dtAction = dtManualActionEvent(DT_ANALYTICS_LOG_EVENT_NAME.DT_PUT_USER_DEVICE_TOKEN)
  try {
    const deviceToken = yield call(getDeviceToken)
    const variables: any = {
      device_platform: Platform.OS.toUpperCase(), // IOS , ANDROID
      device_id: deviceId,
      device_name: deviceName,
      device_token: deviceToken,
    }
    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(putUserDeviceTokenMutation, variables),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    if (response?.data?.data?.putUserDeviceToken?.status === "success") {
      dtAction.reportStringValue('status', 'success')
      storage.save(storage.StorageKey.deviceInformation, variables)
    } else dtAction.reportStringValue('status', 'no_saving')
  } catch (error) {
    dtAction.reportStringValue('status', 'error')
    console.log("PutUserDeviceTokenRequest error", error)
  } finally {
    dtAction.leaveAction()
  }
}

export function* handleSendOTPRequest(action) {
  try {
    const { phoneNumber, email } = action
    const query = {
      phoneNumber: phoneNumber,
      email: email,
    }

    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(sendOtp, query),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    if (!response?.data?.data?.sendOtp) {
      throw response?.data?.data?.sendOtp
    }
    const result = JSON.parse(response?.data?.data?.sendOtp?.result)
    const status = response?.data?.data?.sendOtp?.status
    if (status === "ok" && result?.vToken) {
      yield put(ProfileAction.sendOTPSuccess(result?.vToken, status))
    } else {
      yield put(ProfileAction.sendOTPFailure("error", result))
    }
  } catch (error) {
    yield put(ProfileAction.sendOTPFailure("error", "ERROR"))
  }
}

export function* handleVerifyOTPRequest(action) {
  try {
    const query = {
      input: action.input,
    }

    const response = yield call(restApi, {
      url: env()?.APPSYNC_GRAPHQL_URL,
      method: "post",
      data: graphqlOperation(verifyOtp, query),
      parameters: {},
      headers: {
        "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
      },
    })
    if (!response?.data?.data?.verifyOtp) {
      throw response?.data?.data?.verifyOtp
    }
    const status = response?.data?.data?.verifyOtp?.status
    yield put(ProfileAction.verifyOTPFinished(status))
  } catch (error) {
    yield put(ProfileAction.verifyOTPFinished("error"))
  }
}

export async function getAppConfigurations({ configKey }) {
  try {
    const paramsArray = path.getConfigurations.split(" ")
    const method = paramsArray[0] || "POST"
    const url = env()?.API_GATEWAY_URL + paramsArray[1]
    const data = {
      keys: [configKey],
    }
    const response = await restApi({ url, method, data })
    return !!response?.data?.[configKey]
  } catch (error) {
    return false
  }
}

export function* getAppConfigurationsSaga({ configKey }) {
  try {
    const paramsArray = path.getConfigurations.split(" ")
    const method = paramsArray[0] || "POST"
    const url = env()?.API_GATEWAY_URL + paramsArray[1]
    const data = {
      keys: [configKey],
    }
    const response = yield call(restApi, ({ url, method, data })) 
    return response?.data?.[configKey]
  } catch (error) {
    return false
  }
}


export function* profileSaga() {
  yield all([
    takeLatest(ProfileTypes.PROFILE_REQUEST, getProfile),
    takeLatest(ProfileTypes.PROFILE_UPDATE_REQUEST, updateProfile),
    takeLatest(ProfileTypes.LINK_MEMBERSHIP_REQUEST, linkMembership),
    takeLatest(ProfileTypes.CHANGE_PASSWORD_URL_REQUEST, getChangePasswordUrl),
    takeLatest(ProfileTypes.SUBSCRIPTION_REQUEST, getSubscription),
    takeLatest(ProfileTypes.SUBSCRIPTION_UPDATE_REQUEST, updateSubscription),
    takeLatest(ProfileTypes.GET_DELETE_PAGE_CONTENT, getDeletePageContent),
    takeLatest(ProfileTypes.GET_PROFILE_CONTENT, getProfileContentAem),
    takeLatest(ProfileTypes.PROFILE_UPDATE_LEGACY_USER_REQUEST, updateLegacyUserRequest),
    takeLatest(ProfileTypes.SETTING_NOTIFICATION_REQUEST, settingNotificationRequest),
    takeLatest(
      ProfileTypes.SETTING_NOTIFICATION_EMAIL_SMS_REQUEST,
      settingNotificationEmailSmsRequest,
    ),
    takeLatest(ProfileTypes.CHANGE_SETTING_NOTIFICATION_REQUEST, changeSettingNotificationRequest),
    takeLatest(
      ProfileTypes.CHANGE_SETTING_NOTIFICATION_EMAIL_SMS_REQUEST,
      changeSettingNotificationEmailSmsRequest,
    ),
    takeLatest(ProfileTypes.PUT_USER_DEVICE_TOKEN_REQUEST, putUserDeviceTokenRequest),
    takeLatest(ProfileTypes.SEND_OTP_REQUEST, handleSendOTPRequest),
    takeLatest(ProfileTypes.VERIFY_OTP_REQUEST, handleVerifyOTPRequest),
  ])
}
