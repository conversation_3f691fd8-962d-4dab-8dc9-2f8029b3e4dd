import React, { use<PERSON>ontext, useRef, useEffect, useCallback } from "react"
import {
  FlatList,
  ScrollView,
  TextStyle,
  View,
  ViewStyle,
  TouchableOpacity,
  Platform
} from "react-native"
import { Text } from "../../elements/text/text"
import { color } from "../../theme/color"
import {
  BackGroundGradientWithoutTenantName,
  BackGroundGradientWithTenantName,
} from "ichangi-fe/assets/backgrounds/spotlight-brand-offer"

import { ProductOffer } from "../../components/product-offer/product-offer"
import { ProductOfferType } from "../../components/product-offer/product-offer.props"
import { PLACEHOLDER_ANIMATION_SPEED_IN_MS } from "../../utils/constants"
import ShimmerPlaceHolder from "../../helpers/shimmer-placeholder"
import { useDispatch, useSelector } from "react-redux"
import ShopCreators, { Shop, ShopSelectors } from "../../redux/shopRedux"
import { RootState } from "app/redux/store"
import { DineShopContext } from "../../screens/dine-shop/dine-shop-context"
import { ErrorComponent } from "../../components/error/error"
import { ErrorComponentType } from "../../components/error/error-props"
import { isEmpty, isArray } from "lodash"
import BaseImage from "app/elements/base-image/base-image"
import { REMOTE_CONFIG_FLAGS, isFlagON } from "app/services/firebase/remote-config"
import { DINE_SHOP_TAB_SCREENS, SHOP_SECTIONS } from "app/screens/dine-shop/constants"
import { typography } from "app/theme"
import { ArrowRight } from "assets/icons"
import {
  getExperienceCloudId,
} from "app/services/adobe"
import { getISCInputParamsDeepLink } from "app/helpers/deeplink/deeplink-parameter"
import { NavigationConstants, StateCode } from "app/utils/constants"
import { getDeepLinkV2 } from "app/sagas/pageConfigSaga"
import { WebViewHeaderTypes } from "app/models/enum"
import { useFocusEffect, useNavigation } from "@react-navigation/native"
import { env } from "app/config/env-params"
import {
  DEFAULT_ISHOPCHANGI_URL,
  ISHOPCHANGI_HOMEPAGE_BRAND_OFFER,
} from "app/screens/search-v2/constants"
import GlobalLoadingController from "app/components/global-loading/global-loading-controller"
import { INITIAL_VISIBLE_TAB_CONTENT_HEIGHT, TOP_BAR_VISIBLE_OFFSET_Y } from "app/screens/dine-shop-v2/dine-shop-v2.styles"
import { useDineShopFlags } from "app/screens/dine-shop-v2/dine-shop-v2.hooks"
import { TOP_TAB_PARENT_HEIGHT } from "app/screens/fly/flights/header-tab-bar/HeaderTabNavBar"
import { NO_PADDING_TAB_BAR_HEIGHT, TAB_BAR_PADDING_N_REFINEMENT } from "app/screens/dine-shop-v2/components/dine-shop-tab-bar"

const flatListStyle: ViewStyle = {
  position: "absolute",
  top: 40,
}

const flatListItemsStyle: ViewStyle = {
  marginRight: 16,
  left: 9,
}

const emptyHeaderStyle: ViewStyle = {
  marginLeft: 16,
}

const headerFlatListParentContainerStyle: ViewStyle = {
  flexDirection: "row",
  marginEnd: 16,
}

const specificBrandContainerStyle: ViewStyle = {
  width: 114,
  marginStart: 25,
  marginTop: 30,
}

const specificBrandLogoStyle: ViewStyle = {
  width: 50,
  height: 50,
  borderRadius: 25,
  overflow: "hidden",
}

const specificBrandTextContainerStyle: ViewStyle = {
  marginTop: 16,
}

const backgroundWaveGradientColorStyle: ViewStyle = {
  backgroundColor: color.palette.almostWhiteGrey,
}

const specificBrandTitleStyle: ViewStyle = {
  width: 114,
  overflow: "hidden",
}

const specificBrandSubTitleStyle: ViewStyle = {
  ...specificBrandTitleStyle,
  marginTop: 8,
}

const specificBrandTitleColorStyle: TextStyle = {
  color: color.palette.whiteGrey,
}

const buttonShopAll: ViewStyle = {
  width: 93,
  height: 28,
  justifyContent: 'center',
  alignItems: 'center',
  flexDirection: 'row',
  borderRadius: 60,
  backgroundColor: color.palette.whiteGrey,
  marginTop: 8,
  gap: 4
}

const txtButtonShopAll: TextStyle = {
  fontFamily: typography.bold,
  color: color.palette.lightPurple,
  fontSize: 14,
  fontWeight: Platform.select({ ios: "700", android: "normal" }),
  lineHeight: 18,
}

const handlePrice = (item) => {
  const originalPrice =
    !isEmpty(item?.originalPrice) &&
    isArray(item?.originalPrice) &&
    item?.originalPrice?.find((e) => e?.currency === "SGD")
  const salePrice =
    !isEmpty(item?.salePrice) &&
    isArray(item?.originalPrice) &&
    item?.salePrice?.find((e) => e?.currency === "SGD")

  return { ...item, originalPrice: originalPrice?.value, salePrice: salePrice?.value }
}
const renderFlatListData = (item, type, handleClick, index) => {
  item.type = type
  item.onPressed = () => handleClick(item, index)

  return (
    <View style={flatListItemsStyle}>
      <ProductOffer {...handlePrice(item)} />
    </View>
  )
}

const lightGreyLoadingColors = [color.palette.lightGrey, color.background, color.palette.lightGrey]
const lighterGreyLoadingColors = [
  color.palette.lighterGrey,
  color.background,
  color.palette.lighterGrey,
]

const loadingTextStyles: ViewStyle[] = [
  { width: 100, height: 13, borderRadius: 4 },
  { width: 80, height: 13, borderRadius: 4, marginTop: 8 },
  { width: 60, height: 13, borderRadius: 4, marginTop: 8 },
  { width: 100, height: 13, borderRadius: 4, marginTop: 16 },
]

const renderFlatListHeaderLoading = () => {
  return (
    <View style={specificBrandContainerStyle}>
      <ShimmerPlaceHolder
        duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
        shimmerColors={lightGreyLoadingColors}
        shimmerStyle={specificBrandLogoStyle}
      />
      <View style={specificBrandTextContainerStyle}>
        <ShimmerPlaceHolder
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={lighterGreyLoadingColors}
          shimmerStyle={loadingTextStyles[0]}
        />
        <ShimmerPlaceHolder
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={lighterGreyLoadingColors}
          shimmerStyle={loadingTextStyles[1]}
        />
        <ShimmerPlaceHolder
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={lighterGreyLoadingColors}
          shimmerStyle={loadingTextStyles[2]}
        />

        <ShimmerPlaceHolder
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={lighterGreyLoadingColors}
          shimmerStyle={loadingTextStyles[3]}
        />
        <ShimmerPlaceHolder
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={lighterGreyLoadingColors}
          shimmerStyle={loadingTextStyles[1]}
        />
        <ShimmerPlaceHolder
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={lighterGreyLoadingColors}
          shimmerStyle={loadingTextStyles[2]}
        />
      </View>
    </View>
  )
}

const renderFlatListHeader = (payload) => {
  const navigation = useNavigation<any>()
  if (!payload) {
    return <View style={emptyHeaderStyle}></View>
  }

  const { sectionTitle, sectionSubTitle, logo } = payload

  const handleNavigateCSMIShopchangi = async (url: string) => {
    GlobalLoadingController.showLoading()
    const ecid = await getExperienceCloudId()
    const target = getISCInputParamsDeepLink(url)
    const payload = {
      stateCode: StateCode.ISHOPCHANGI,
      input: {
        ...target,
        ecid,
      },
    }
    try {
      const response = await getDeepLinkV2(payload, true)
      if (response?.redirectUri) {
        navigation.navigate(NavigationConstants.playpassWebview, {
          uri: response?.redirectUri,
          needBackButton: true,
          needCloseButton: true,
          headerType: WebViewHeaderTypes.default,
          basicAuthCredential: response?.basicAuth,
        })
      } else {
        navigation.navigate(NavigationConstants.webview, {
          uri: url,
        })
      }
    } catch (error) {
      navigation.navigate(NavigationConstants.webview, {
        uri: url,
      })
    } finally {
      GlobalLoadingController.hideLoading()
    }
  }

  const onPressShopAll = () => {
    const ISHOPCHANGI_DOMAIN = env()?.ISHOPCHANGI_URL || DEFAULT_ISHOPCHANGI_URL
    handleNavigateCSMIShopchangi(`${ISHOPCHANGI_DOMAIN}${ISHOPCHANGI_HOMEPAGE_BRAND_OFFER}`)
  }

  return (
    <View style={specificBrandContainerStyle}>
      <BaseImage style={specificBrandLogoStyle} source={{ uri: logo }} />
      <View style={specificBrandTextContainerStyle}>
        <View style={specificBrandTitleStyle}>
          <Text
            text={sectionTitle}
            preset="h4"
            style={specificBrandTitleColorStyle}
            numberOfLines={4}
          />
        </View>
        <View style={specificBrandSubTitleStyle}>
          <Text
            text={sectionSubTitle}
            preset="caption1Regular"
            style={specificBrandTitleColorStyle}
            numberOfLines={4}
          />
        </View>
      </View>
      <TouchableOpacity style={buttonShopAll} onPress={onPressShopAll}>
        <Text
          tx="dineShopScreen.shopAll"
          style={txtButtonShopAll}
        />
        <ArrowRight width={16} height={16} />
      </TouchableOpacity>
    </View>
  )
}

const BrandOffer = (props) => {
  const {styleContainerProps} = props
  const dispatch = useDispatch()
  const sectionRef = useRef(null)
  const isScrolledRef = useRef(false)
  const {isShopDineEpicV2On} = useDineShopFlags()

  const handlers = useContext(DineShopContext).Handlers
  const handleClick = handlers.shop.handleSpotlightBrandOfferComponentClick

  const [isOnFlagJFY, setFlagJFY] = React.useState(false)
  const { spotlightBrandOfferPayload, pageLevelState }: any = useSelector<RootState, Shop>((data) =>
    ShopSelectors.spotlightBrandOfferData(data),
  )
  const isLoading = ProductOfferType.loading === spotlightBrandOfferPayload?.type

  let containerStyle: ViewStyle
  const brandOfferIndex = pageLevelState?.children?.findIndex((item) => item?.name === "brandOffer")
  if (brandOfferIndex === 0) {
    containerStyle = { marginTop: 0 }
  }

  const CONTAINER_MARGIN_BOTTOM = 50

  const marginBottomStyle: ViewStyle = {
    marginBottom: CONTAINER_MARGIN_BOTTOM,
  }

  const loadSpotlightBrandOffer = () => {
    dispatch(ShopCreators.shopSpotlightBrandOfferRequest())
  }

  useEffect(() => {
    const checkShowJFY = () => {
      const isOn = isFlagON(REMOTE_CONFIG_FLAGS.SHOP_JUSTFORYOU)
      setFlagJFY(isOn)
    }
    checkShowJFY()
  }, [])

  useEffect(() => {
    if (isOnFlagJFY) {
      loadSpotlightBrandOffer()
    }
  }, [isOnFlagJFY])

  useEffect(() => {
    const {section, screen} = handlers.shop.routeParams || {}

    if (
      !isLoading &&
      !isScrolledRef?.current &&
      section === SHOP_SECTIONS.ishopchangi &&
      screen === DINE_SHOP_TAB_SCREENS.shop &&
      spotlightBrandOfferPayload?.products?.length
    ) {
      setTimeout(() => {
        sectionRef?.current?.measureLayout?.(
          handlers.shop?.overallScrollRef?.current?.getInnerViewNode?.(),
          (x: number, y: number) => {
            isScrolledRef.current = true
            const otherSectionsEffectValue = isShopDineEpicV2On ? NO_PADDING_TAB_BAR_HEIGHT + TAB_BAR_PADDING_N_REFINEMENT + CONTAINER_MARGIN_BOTTOM : 0
            handlers.shop.overallScrollRef?.current?.scrollTo?.({
              y: y - otherSectionsEffectValue,
              animated: true,
            })
          },
        )
      }, 2500);
    }
  }, [
    isLoading,
    isShopDineEpicV2On,
    sectionRef?.current,
    handlers?.shop?.routeParams,
    spotlightBrandOfferPayload?.products?.length,
  ])

  useFocusEffect(
    useCallback(() => {
      return () => {
        isScrolledRef.current = false
      }
    }, [])
  )

  // priority flag above the other conditions
  if (!isOnFlagJFY) {
    return <View />
  }

  if (spotlightBrandOfferPayload?.errorFlag) {
    return (
      <ErrorComponent
        type={ErrorComponentType.standard}
        onPressed={() => {
          loadSpotlightBrandOffer()
        }}
      />
    )
  }

  if (!spotlightBrandOfferPayload || spotlightBrandOfferPayload?.products.length === 0) {
    return <></>
  }

  const isTenantName = spotlightBrandOfferPayload.isTenantNameAvailable

  const handleTenantName = () => {
    if (isLoading) {
      return (
        <BackGroundGradientWithTenantName
          preserveAspectRatio="xMinYMax slice"
          width="100%"
          style={backgroundWaveGradientColorStyle}
        />
      )
    } else {
      if (isTenantName) {
        return (
          <BackGroundGradientWithTenantName
            preserveAspectRatio="xMinYMax slice"
            width="100%"
            style={backgroundWaveGradientColorStyle}
          />
        )
      } else {
        return (
          <BackGroundGradientWithoutTenantName
            preserveAspectRatio="xMinYMax slice"
            width="100%"
            style={backgroundWaveGradientColorStyle}
          />
        )
      }
    }
  }
  return (
    <View ref={sectionRef} style={[containerStyle, marginBottomStyle, styleContainerProps]}>
      {handleTenantName()}
      <ScrollView
        scrollEnabled={true}
        style={flatListStyle}
        horizontal
        showsHorizontalScrollIndicator={false}
        nestedScrollEnabled
      >
        <View style={headerFlatListParentContainerStyle}>
          {isLoading
            ? renderFlatListHeaderLoading()
            : renderFlatListHeader(spotlightBrandOfferPayload)}
          <View>
            <FlatList
              data={spotlightBrandOfferPayload.products}
              renderItem={({ item, index }) =>
                renderFlatListData(item, spotlightBrandOfferPayload.type, handleClick, index)
              }
              showsHorizontalScrollIndicator={false}
              horizontal
              scrollEnabled={false}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  )
}

export { BrandOffer }
