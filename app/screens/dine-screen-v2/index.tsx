import React from "react"
import { <PERSON>, ScrollView } from "react-native"
import { styles } from "./styles"
import { <PERSON>Link, DealsPromos, HeroBanner, <PERSON>eGuid<PERSON>, ViewJustForYouV2 } from "./component"
import { useFunctions } from "./useFunction"
import { useNavigation } from "@react-navigation/native"
import { handleCondition } from "app/utils"
import { BrandOffer } from "app/sections/brand-offer/brand-offer"

const DineScreenV2 = () => {
  const navigation = useNavigation()
  const {
    dataDealsPromos,
    isLoadingDealsPromos,
    isErrorDealsPromos,
    isJustForYouV2,
    dataJustForYou,
    isLoadingJustForYou,
    isErrorJustForYou,
    dataDineGuides,
    isLoadingDineGuides,
    isErrorDineGuides,
  } = useFunctions()

  return (
    <ScrollView showsVerticalScrollIndicator={false} style={styles.background}>
      <View style={styles.container}>
        <QuickLink />
        <HeroBanner />
        <DealsPromos
          data={dataDealsPromos}
          isLoading={isLoadingDealsPromos}
          isError={isErrorDealsPromos}
          navigation={navigation}
        />
        {handleCondition(isJustForYouV2,
          <ViewJustForYouV2
            dataJustForYou={dataJustForYou}
            isLoadingJustForYou={isLoadingJustForYou}
            isErrorJustForYou={isErrorJustForYou}
          />,
          <View style={styles.viewJustForYou}>
            <BrandOffer styleContainerProps={styles.marginBottom}/>
          </View>
        )}
        <DineGuides
          dataDineGuides={dataDineGuides}
          isLoadingDineGuides={isLoadingDineGuides}
          isErrorDineGuides={isErrorDineGuides}
        />
        <View style={styles.viewBottom} />
      </View>
    </ScrollView>
  )
}

export { DineScreenV2 }
