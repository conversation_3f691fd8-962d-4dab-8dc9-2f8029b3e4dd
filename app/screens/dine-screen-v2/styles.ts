import { color } from "app/theme/color"
import { StyleSheet, Dimensions } from "react-native"

const widthScreen = Dimensions.get("window").width

const styles = StyleSheet.create({
  background: {
    backgroundColor: color.palette.whiteGrey,
  },
  container: {
    width: widthScreen,
    backgroundColor: color.palette.whiteGrey,
    paddingTop: 40,
  },
  viewJustForYou: {
    width: "100%",
    marginTop: 50,
  },
  viewBottom: {
    marginBottom: 100
  },
  marginBottom: { marginBottom: 0 }
})

export { styles }
