import * as React from "react"
import { Dimensions, Pressable, View, ViewStyle, TouchableOpacity, ImageStyle } from "react-native"
import Carousel from "react-native-reanimated-carousel"
import { Text } from "app/elements/text/text"
import ShimmerPlaceHolder from "app/helpers/shimmer-placeholder"
import { PLACEHOLDER_ANIMATION_SPEED_IN_MS } from "app/utils/constants"
import { styles, lightGreyLoadingColors, loadingElementsLayout } from "./image-carousel.styles"
import BaseImage from "app/elements/base-image/base-image"

const { width } = Dimensions.get("window")
const COMPONENT_NAME = "MainPromo"

const PaginationIndicator = ({ label }) => {
  return (
    <View style={styles.paginationContent}>
      <Text preset="caption2Bold" style={styles.paginationTextStyle}>
        {label}
      </Text>
    </View>
  )
}

const loadingView = () => {
  return (
    <View style={styles.container}>
      <ShimmerPlaceHolder
        shimmerColors={lightGreyLoadingColors}
        duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
        shimmerStyle={loadingElementsLayout[0]}
      />
      <View style={styles.contentCarouselContainer}>
        <ShimmerPlaceHolder
          shimmerColors={lightGreyLoadingColors}
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerStyle={loadingElementsLayout[1]}
        />
      </View>
    </View>
  )
}

const contentView = (
  sortedPromotions: MainPromoDto[],
  onPressed: any,
  testID: string,
  accessibilityLabel: string,
  itemWidth = width - 32,
) => {
  const [index, setIndex] = React.useState(0)

  const pagingText = index + 1 + "/" + sortedPromotions?.length

  const onSnapToItem = (index: number) => {
    setIndex(index)
  }

  const renderCarouselItem = ({ item, index: indexs }) => {
    return (
      <Pressable
        key={`${item?.orderId}-${indexs}`}
        onPress={() => onPressed?.(item)}
        testID={`${testID}__PressablePromotions__${indexs}`}
        accessibilityLabel={`${accessibilityLabel}__PressablePromotions__${indexs}`}
        accessible={false}
      >
        <BaseImage
          style={styles.backgroundImageStyle}
          source={{ uri: item?.image }}
          resizeMode={"cover"}
          testID={`${COMPONENT_NAME}__Image`}
          accessibilityLabel={item?.image}
        />
        {sortedPromotions?.length > 1 && (
          <View style={styles.paginationStyle}>
            <PaginationIndicator label={pagingText} />
          </View>
        )}
      </Pressable>
    )
  }

  return (
    <View style={styles.container}>
      <Carousel
        loop={true}
        data={sortedPromotions}
        style={styles.carouselContainer}
        renderItem={renderCarouselItem}
        width={itemWidth}
        testID={`${testID}__Carousel`}
        onSnapToItem={onSnapToItem}
      />
      {sortedPromotions.length > 0 && (
        <TouchableOpacity
          style={styles.contentCarouselContainer}
          onPress={() => onPressed?.(sortedPromotions[index])}
        >
          <Text
            preset={"bodyTextBold"}
            style={styles.titleStyle}
            numberOfLines={2}
            text={sortedPromotions[index].promoTitle}
            testID={`${COMPONENT_NAME}__Title`}
            accessibilityLabel={sortedPromotions[index].promoTitle}
          />
          <Text
            preset={"caption1Regular"}
            style={styles.descriptionStyle}
            numberOfLines={1}
            text={sortedPromotions[index].subCopy}
            testID={`${COMPONENT_NAME}__SubCopy`}
            accessibilityLabel={sortedPromotions[index].subCopy}
          />
        </TouchableOpacity>
      )}
    </View>
  )
}

export enum NavigationType {
  inapp = "in-app",
  external = "external",
}

export enum MainPromoType {
  default = "default",
  loading = "loading",
}

export interface MainPromoProps {
  promotions?: MainPromoDto[]
  type: MainPromoType
  onPressed?: any
  waveComponent?: React.ReactNode
  testID?: string
  accessibilityLabel?: string
  sliderWidth?: number
  itemWidth?: number
  containerStyle?: ViewStyle
  carouselContainerStyle?: ViewStyle
  backgroundImageStyle?: ImageStyle
}

export interface MainPromoDto {
  orderId: number // assuming that backend want to decide the order of promotion
  sku?: string
  offerId?: string
  tenantId?: string
  image: string
  promoTitle: string
  subCopy: string
  linkURL?: string
  navigationType?: NavigationType
  navigationValue?: string
  redirect?: any
}

export function ImageCarousel(props: MainPromoProps) {
  const {
    promotions,
    type,
    onPressed,
    testID = "MainPromo",
    accessibilityLabel = "MainPromo",
    containerStyle,
  } = props
  const isLoading = type === MainPromoType.loading

  return (
    <View style={[styles.containerStyle, containerStyle]}>
      {isLoading ? loadingView() : contentView(promotions, onPressed, testID, accessibilityLabel)}
    </View>
  )
}
