import { color } from "app/theme"
import { ViewStyle, StyleSheet, Dimensions } from "react-native"

const { width } = Dimensions.get("screen")
const PADDING_HORIZONTAL_CAROUSEL = 16
const WIDTH_IMAGE = width - PADDING_HORIZONTAL_CAROUSEL * 2
const RATIO = 344 / 218 // Follow base ratio from figma: Width: 344 and Height: 218
const HEIGHT_IMAGE = WIDTH_IMAGE / RATIO
export const loadingElementsLayout: ViewStyle[] = [
  {
    borderRadius: 16,
    height: 218,
    width: "100%",
  },
  {
    height: 16,
    borderRadius: 4,
    width: `${(240 / WIDTH_IMAGE) * 100}%`,
  },
]

export const lightGreyLoadingColors = [
  color.palette.lighterGrey,
  color.background,
  color.palette.lighterGrey,
]

export const styles = StyleSheet.create({
  container: {
    paddingHorizontal: PADDING_HORIZONTAL_CAROUSEL,
  },
  backgroundImageStyle: {
    borderRadius: 16,
    height: HEIGHT_IMAGE,
    width: WIDTH_IMAGE,
  },
  carouselContainer: {
    height: HEIGHT_IMAGE,
    backgroundColor: color.palette.whiteGrey,
  },
  containerStyle: {
    backgroundColor: color.palette.whiteGrey,
  },
  contentCarouselContainer: {
    marginTop: 12,
    width: "100%",
    paddingHorizontal: 4,
  },
  contentContainerLayout: {
    justifyContent: "center",
    marginEnd: 24,
    marginStart: 24,
    marginTop: -102,
  },
  descriptionStyle: {
    color: color.palette.almostBlackGrey,
    textTransform: "none",
  },
  lowerCaseTitle: {
    letterSpacing: 0,
    textTransform: "none",
  },
  paginationStyle: {
    bottom: 12,
    position: "absolute",
    right: 25,
  },
  paginationContent: {
    paddingHorizontal: 16,
    paddingVertical: 4,
    backgroundColor: "rgba(18, 18, 18, 0.8)",
    borderRadius: 16,
    flex: 1,
    alignSelf: "flex-start",
  },
  paginationTextStyle: {
    color: color.palette.whiteGrey,
  },
  subCopyStyle: {
    color: color.palette.almostBlackGrey,
  },
  titleStyle: {
    color: color.palette.almostBlackGrey,
    marginBottom: 4,
    textTransform: "none",
  },
})
