import { useState, useEffect, useCallback, useRef } from "react"
import { ErrorType } from "./dine-shop-directory.constants"
import NetInfo from "@react-native-community/netinfo"
import requestApi from "app/services/api/request"
import { env } from "app/config/env-params"
import { graphqlOperation } from "aws-amplify"
import { getTenantListing, listFilterPillDineShop_v2 } from "app/models/queries"
import { filterDataListByDataFilterV2, getInitialDataFilter, toggleChildTagActive } from "./dine-shop-directory-until"
import { getDataDineDirectoryInday, setDataDineDirectoryInday, setDateSaveDineDirectoryInday, getDateSaveDineDirectoryInday } from "app/utils/storage/mmkv-storage"
import moment from 'moment';
import { useNavigation } from "@react-navigation/native"

const useFunction = (initSelectedData) => {
  const navigation = useNavigation<any>()
  const rootListRef = useRef(null)
  const perkItemOffsetListRef = useRef([])
  const rootItemOffsetRef = useRef([])
  const dataDateToSaveValue = getDateSaveDineDirectoryInday()
  const dataDineDirectoryInDay = getDataDineDirectoryInday()

  const [error, setError] = useState<ErrorType | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [data, setData] = useState(null)
  const [dataOriginal, setDataOriginal] = useState(null)

  const [errorFilter, setErrorFilter] = useState(false)
  const [loadingFilter, setLoadingFilter] = useState(true)
  const [dataFilter, setDataFilter] = useState(null)
  const [dataFilterOriginal, setDataFilterOriginal] = useState(null)

  const [showFilterModal, setShowFilterModal] = useState(false)
  const [offsetRecalculationCount, setOffsetRecalculationCount] = useState(0)

  const openModalFilter = useCallback(() => {
    setShowFilterModal(true)
  }, [])
  const closeModalFilter = useCallback(() => {
    setShowFilterModal(false)
  }, [])

  const checkInternet = async () => {
    const { isConnected } = await NetInfo.fetch()
    if (!isConnected) {
      setError(ErrorType.NoInternet)
    } else {
      getDataFilter()
      getData()
    }
  }

  useEffect(() => {
    checkInternet()
  }, [])

  const getDataFilter = async () => {
    try {
      setLoadingFilter(true)
      setErrorFilter(null)
      const response = await requestApi({
        url: env()?.APPSYNC_GRAPHQL_URL,
        method: "post",
        data: graphqlOperation(listFilterPillDineShop_v2),
        parameters: {},
        headers: {
          "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
          'Cache-Control': 'no-cache',
        },
      })
      const queryFilterData = response?.data?.data?.listFilterPillDineShop_v2?.data
      if (queryFilterData) {
        const queryFilterData = response?.data?.data?.listFilterPillDineShop_v2?.data
        const initialDataFilter = getInitialDataFilter(queryFilterData, initSelectedData)
        setDataFilter(initialDataFilter)
        setDataFilterOriginal(queryFilterData)
        setLoadingFilter(false)
      } else {
        setLoadingFilter(false)
        setErrorFilter(true)
      }
    } catch (error) {
      setLoadingFilter(false)
      setErrorFilter(true)
    }
  }

  const setDataInDay = () => {
    const date = moment().format("DD/MM/YYYY")
    setDateSaveDineDirectoryInday(date)
  }

  useEffect(() => {
    if (dataDateToSaveValue === moment().format("DD/MM/YYYY")) {
      if (dataFilterOriginal?.length > 0 || errorFilter === true) {
        setLoading(false)
      }
    }
  }, [dataFilterOriginal, errorFilter, dataDateToSaveValue])

  const getData = async () => {
    if (dataDateToSaveValue === moment().format("DD/MM/YYYY")) {
      setError(null)
      setLoading(true)
      setData(
        typeof dataDineDirectoryInDay === "string"
          ? JSON.parse(dataDineDirectoryInDay)
          : []
      )
      setDataOriginal(
        typeof dataDineDirectoryInDay === "string"
          ? JSON.parse(dataDineDirectoryInDay)
          : []
      )
    } else {
      try {
        setLoading(true)
        setError(null)
        const response = await requestApi({
          url: env()?.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: graphqlOperation(getTenantListing),
          parameters: {},
          headers: {
            "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
            'Cache-Control': 'no-cache',
          },
        })
        if (response?.data?.data?.getResultScreenByFilterDine_v3?.nodes || response?.data?.data?.getResultScreenByFilterShop_v3?.nodes) {
          const dataDine = response?.data?.data?.getResultScreenByFilterDine_v3?.nodes
          const dataShop = response?.data?.data?.getResultScreenByFilterShop_v3?.nodes

          let dataCombined = [...(dataDine || []), ...(dataShop || [])]
          dataCombined = dataCombined.sort((a, b) => {
            const titleA = (a.title || "").toUpperCase()
            const titleB = (b.title || "").toUpperCase()
            if (titleA < titleB) return -1
            if (titleA > titleB) return 1
            return 0
          })
          setDataInDay()
          setDataDineDirectoryInday(JSON.stringify(dataCombined))
          setData(dataCombined)
          setDataOriginal(dataCombined)
          setLoading(false)
        } else {
          setLoading(false)
          setError(ErrorType.ErrorDefault)
        }
      } catch (error) {
        setLoading(false)
        setError(ErrorType.ErrorDefault)
      }
    }
  }

  const handlePressReloadError = () => {
    if (error === ErrorType.NoInternet) {
      checkInternet()
    } else {
      getDataFilter()
      getData()
    }
  }

  const handlePressReloadFilter = () => {
    getDataFilter()
  }

  const setDataSubFilter = (type) => {
    const parentTagTitle =
      dataFilter.find((item) => item.childTags?.some((child) => child.tagName === type))
        ?.tagTitle || "Location"
    const value = toggleChildTagActive(dataFilter, parentTagTitle, type)
    setDataFilter(value)
  }

  useEffect(() => {
    if (dataFilter && dataOriginal?.length > 0) {
      const dataValueAfterFilter = filterDataListByDataFilterV2(dataOriginal, dataFilter);
      setData(dataValueAfterFilter)
      rootListRef.current?.scrollToOffset({ offset: 0, animated: false })
    }
  }, [JSON.stringify(dataFilter)])

  return {
    navigation,
    error,
    loading,
    data,
    handlePressReloadError,
    showFilterModal,
    openModalFilter,
    closeModalFilter,
    errorFilter,
    loadingFilter,
    dataFilter,
    setDataFilter,
    handlePressReloadFilter,
    dataFilterOriginal,
    rootListRef,
    setDataSubFilter,
    perkItemOffsetListRef,
    rootItemOffsetRef,
    offsetRecalculationCount,
    setOffsetRecalculationCount,
  }
}

export { useFunction }
