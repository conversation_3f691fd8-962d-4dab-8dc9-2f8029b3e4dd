import { color } from "app/theme/color"
import { StyleSheet } from "react-native"

const heightHeader = 100

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: color.palette.almostWhiteGrey,
  },
  viewContent: {
    flex: 1,
    backgroundColor: color.palette.almostWhiteGrey,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  viewHeaderAnimated: {
    width: "100%",
    position: "absolute",
    top: 0,
  },
  viewFilterHeader: {
    position: "absolute",
    top: heightHeader,
    backgroundColor: color.palette.whiteGrey,
  },
  background: {
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    backgroundColor: color.palette.whiteGrey,
  },
  contentContainerStyle: {
    backgroundColor: color.palette.almostWhiteGrey,
    paddingBottom: 24,
  },
})
