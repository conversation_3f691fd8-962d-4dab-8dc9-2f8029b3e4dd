import React from 'react';
import { StyleSheet, View } from 'react-native';
import ShimmerPlaceholder from "app/helpers/shimmer-placeholder"
import { LOADING_COLORS } from "app/screens/for-you/components/miffy-gamification-banner/miffy-gamification-banner.styles"
import { PLACEHOLDER_ANIMATION_SPEED_IN_MS } from 'app/utils/constants';
import { color } from 'app/theme/color';
import { BACKGROUND_IMAGE_HEIGHT } from 'app/components/collapsible-header';
import { FILTER_BORDER_RADIUS } from 'app/components/collapsible-header/styles';

const data = [1, 2, 3, 4, 5]

const ViewLoading = React.memo(() => {
  return (
    <View style={styles.container}>
      {data.map(() => {
        return (
          <View style={styles.itemLoading} key={Math.random()}>
            <ShimmerPlaceholder
              duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
              shimmerColors={LOADING_COLORS}
              shimmerStyle={styles.imageLoading}
            />
            <View style={styles.viewColumn}>
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={LOADING_COLORS}
                shimmerStyle={styles.firstLoading}
              />
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={LOADING_COLORS}
                shimmerStyle={styles.seccondLoading}
              />
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={LOADING_COLORS}
                shimmerStyle={styles.thirdLoading}
              />
            </View>
          </View>
        )
      })}
    </View>
  )
});

const styles = StyleSheet.create({
  container: {
    marginTop: BACKGROUND_IMAGE_HEIGHT,
    paddingTop: 80,
    borderTopLeftRadius: FILTER_BORDER_RADIUS,
    borderTopRightRadius: FILTER_BORDER_RADIUS,
    height: "100%",
    overflow: "hidden",
    backgroundColor: color.palette.whiteGrey,
    paddingHorizontal: 20,
    gap: 24
  },
  itemLoading: {
    width: '100%',
    flexDirection: 'row'
  },
  imageLoading: {
    width: 48,
    height: 48,
    borderRadius: 8,
    marginRight: 16
  },
  viewColumn: {
    flex: 1,
    gap: 12,
    paddingBottom: 24,
    borderBottomWidth: 1,
    borderColor: color.palette.lighterGrey,
  },
  firstLoading: {
    width: '100%',
    height: 12,
    borderRadius: 4
  },
  seccondLoading: {
    width: 148,
    height: 12,
    borderRadius: 4
  },
  thirdLoading: {
    width: 80,
    height: 12,
    borderRadius: 4
  }
})

export { ViewLoading };