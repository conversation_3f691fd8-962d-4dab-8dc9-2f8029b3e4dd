import React from 'react';
import { StyleSheet, View } from 'react-native';
import { presets, Text } from 'app/elements/text';
import { color } from 'app/theme';
import { DineEmpty } from 'assets/icons';
import { FILTER_BORDER_RADIUS } from 'app/components/collapsible-header/styles';

const ViewEmpty = React.memo((props: any) => {
  return (
    <View style={styles.container}>
      <View style={styles.container}>
        <DineEmpty width={120} height={120} />
        <Text style={styles.titleTextStyle} tx={'dineShopDirectory.titleEmpty'} />
        <Text style={styles.messageTextStyle} tx={'dineShopDirectory.contentEmpty'} />
      </View>
    </View>
  )
});

const styles = StyleSheet.create({
  container: {
    marginTop: 26,
    borderTopLeftRadius: FILTER_BORDER_RADIUS,
    borderTopRightRadius: FILTER_BORDER_RADIUS,
    height: "100%",
    overflow: "hidden",
    paddingHorizontal: 24,
    alignItems: 'center'
  },
  titleTextStyle: {
    ...presets.h2,
    marginBottom: 16,
    marginTop: 40,
    textAlign: "center",
  },
  messageTextStyle: {
    ...presets.caption1Regular,
    color: color.palette.darkestGrey,
    textAlign: "center",
  },
  reloadButtonStyle: {
    width: '100%',
    borderRadius: 60,
    paddingHorizontal: 24,
    marginTop: 24
  },
})

export { ViewEmpty }