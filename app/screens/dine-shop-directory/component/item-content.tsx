import { Text } from 'app/elements/text';
import { color, typography } from 'app/theme';
import React, { useCallback } from 'react';
import { Platform, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Status, COMPONENT_NAME, DineShopType } from '../dine-shop-directory.constants';
import BaseImage from 'app/elements/base-image/base-image';
import LinearGradient from 'react-native-linear-gradient';
import { useNavigation } from '@react-navigation/native'
import { NavigationConstants } from 'app/utils/constants';

const PERK_ITEM_FIXED_MARGIN = 12

const ItemContent = React.memo((props: any) => {
  const navigation = useNavigation<any>();
  const { item, index, offsetRecalculationCount,
    perkItemOffsetListRef,
    setOffsetRecalculationCount, dataLength } = props

  const onPressItem = useCallback(() => {
    switch (item?.tenantType) {
      case DineShopType.SHOP:
        navigation.navigate(NavigationConstants.shopDetailsScreen, {
          tenantId: item?.id,
          name: "",
        })
        break
      case DineShopType.DINE:
        navigation.navigate(NavigationConstants.restaurantDetailScreen, {
          tenantId: item?.id,
          name: "",
        })
        break
      default:
        navigation.navigate(NavigationConstants.shopDetailsScreen, {
          tenantId: item?.id,
          name: "",
        })
        break
    }
  }, [item, navigation]);

  const renderStatus = () => {
    if (item?.openingStatus === Status.Open) {
      return <Text style={[styles.txtStatusGreen, styles.absoluteStyle]} testID={`${COMPONENT_NAME}__Item__${index}__Status_Open`}>{item?.openingStatus}</Text>
    } else if (item?.openingStatus === Status.OpenSomeOutlets) {
      return <View style={[styles.viewSomeOutLet, styles.absoluteStyle]}>
        <Text style={styles.txtStatusGreen}>Open</Text>
        <Text style={[styles.txtStatusGray, { color: color.palette.darkGrey999 }]}>Some Outlets</Text>
      </View>
    } else {
      return <Text style={[styles.txtStatusGray, styles.absoluteStyle]} testID={`${COMPONENT_NAME}__Item__${index}__Status_Closed`}>{item?.openingStatus}</Text>
    }
  }

  return (
    <TouchableOpacity onPress={onPressItem} style={[styles.container, { marginTop: index === 0 ? 0 : 20 }]} activeOpacity={1}
      onLayout={(event) => {
        const layoutHeight = event.nativeEvent.layout.height + PERK_ITEM_FIXED_MARGIN
        if (!perkItemOffsetListRef) return
        if (!perkItemOffsetListRef?.current) {
          perkItemOffsetListRef.current = {
            [index]: layoutHeight,
          }
        } else {
          perkItemOffsetListRef.current[index] = layoutHeight
        }
      }}
    >
      <BaseImage
        accessibilityLabel={`${COMPONENT_NAME}__Item__${index}__Icon`}
        source={{ uri: item?.logoImage }}
        style={styles.image}
        testID={`${COMPONENT_NAME}__Item__${index}__Icon`}
      />
      <View style={styles.viewContent}>
        <View style={styles.viewText}>
          <Text style={styles.txtTitle} testID={`${COMPONENT_NAME}__Item__${index}__Title`} numberOfLines={2}>{item?.title}</Text>
          {item?.location_display?.length > 0 && <View style={styles.viewRowText}>
            <Text style={styles.txtGate} testID={`${COMPONENT_NAME}__Item__${index}__Gate`}>{item?.location_display}</Text>
            {item?.area_display?.length > 0 && <Text style={styles.txtStatusGate} testID={`${COMPONENT_NAME}__Item__${index}__GateStatus`}> · {item?.area_display}</Text>}
          </View>}
          <View style={styles.viewRow}>
            {item?.staffPerk === true && <View style={styles.viewTagDefault}>
              <Text tx="dineShopDirectory.tagStaffPerks" style={styles.txtTagDefault} />
            </View>}
            {item?.iscAvailable === true && <LinearGradient
              colors={color.palette.iShopChangiGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.viewTagGradient}
            >
              <Text tx="dineShopDirectory.tagAvailableonIShopChangi" style={styles.txtTagGradient} />
            </LinearGradient>}
          </View>
          {item?.label && <Text style={styles.txtService} testID={`${COMPONENT_NAME}__Item__${index}__Service`} numberOfLines={2}>{item?.label}</Text>}
        </View>
        {renderStatus()}
      </View>
    </TouchableOpacity>
  )
});

const styles = StyleSheet.create({
  container: {
    width: '100%',
    flexDirection: 'row',
    paddingHorizontal: 20,
  },
  image: {
    width: 48,
    height: 48,
    borderRadius: 8,
    marginRight: 16,
  },
  viewContent: {
    flex: 1,
    flexDirection: 'row',
    paddingBottom: 24,
    borderColor: color.palette.lighterGrey,
    borderBottomWidth: 1
  },
  viewText: {
    flex: 1,
  },
  txtStatusGreen: {
    fontFamily: typography.bold,
    color: color.palette.basegreen,
    fontSize: 11,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 15,
    marginLeft: 16,
  },
  txtStatusGray: {
    fontFamily: typography.bold,
    color: color.palette.darkestGrey,
    fontSize: 11,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 15,
    marginLeft: 16,
  },
  txtTitle: {
    fontFamily: typography.black,
    color: color.palette.almostBlackGrey,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    lineHeight: 20,
    maxWidth: '80%',
  },
  viewRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4
  },
  txtGate: {
    fontFamily: typography.bold,
    color: color.palette.almostBlackGrey,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 18,
  },
  txtStatusGate: {
    fontFamily: typography.regular,
    color: color.palette.darkestGrey,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 18,
  },
  txtService: {
    fontFamily: typography.regular,
    color: color.palette.darkestGrey,
    fontSize: 12,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 16,
  },
  viewSomeOutLet: {
    alignItems: 'flex-end',
    gap: 2,
  },
  absoluteStyle: {
    position: 'absolute',
    top: 0,
    right: 0
  },
  viewTagDefault: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    borderColor: color.palette.lightestPurple,
    borderWidth: 1,
    marginBottom: 4,
  },
  viewTagGradient: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginBottom: 4,
  },
  txtTagDefault: {
    fontFamily: typography.bold,
    color: color.palette.pink700,
    fontSize: 11,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 14,
  },
  txtTagGradient: {
    fontFamily: typography.bold,
    color: color.palette.whiteGrey,
    fontSize: 11,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 14,
  },
  viewRowText: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    marginBottom: 12
  }
})

export { ItemContent }
