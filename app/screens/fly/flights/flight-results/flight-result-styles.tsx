import { color } from "app/theme"
import { StyleSheet } from "react-native"
import { presets } from "app/elements/text/text.presets"

export const departureStyles = StyleSheet.create({
  badgeViewStyle: {
    alignSelf: "flex-end",
    position: "absolute",
    right: -3,
    top: -3,
    zIndex: 1,
  },
  bottomContainer: {
    backgroundColor: color.palette.lightestGrey,
    paddingHorizontal: 26,
    paddingVertical: 16,
  },
  calendarContainer: {
    borderRightColor: color.palette.lightGrey,
    borderRightWidth: 0.5,
    flex: 0.89,
    flexDirection: "row",
    paddingHorizontal: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  calendarIconstyle: {
    marginRight: 10,
  },
  containerFilter: {
    paddingHorizontal: 24,
  },
  contentContainerStyle: {
    flex: 1,
  },
  dateFilterStyles: {
    ...presets.bodyTextBlackRegular,
    lineHeight: 20,
  },
  emptyView: {
    height: 50,
    justifyContent: "center",
  },
  errorContainerStyles: {
    backgroundColor: color.palette.lightestGrey,
    flex: 1,
  },
  errorOverlayStyle: {
    height: "80%",
    justifyContent: "flex-start",
    paddingVertical: 30,
    width: "100%",
  },
  fabArrowStyle: {
    position: "absolute",
    zIndex: 1,
  },
  fabContainerViewStyle: {
    alignItems: "center",
    alignSelf: "flex-end",
    bottom: 40,
    elevation: 5,
    justifyContent: "center",
    position: "absolute",
    right: 12,
  },
  fabTouchViewStyle: {
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "flex-end",
  },
  feedBackToastStyle: {
    bottom: 24,
    paddingHorizontal: 16,
    width: "100%",
  },
  filterContainerStyles: {
    alignContent: "center",
    backgroundColor: color.palette.whiteGrey,
    borderColor: color.palette.lightGrey,
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: "row",
    height: 44,
    justifyContent: "center",
  },
  flatListHeaderStyles: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    marginVertical: 12,
    paddingLeft: 24,
    paddingRight: 40,
  },
  iconViewStyles: {
    padding: 2,
  },
  inputFieldSearchStyle: {
    backgroundColor: color.palette.transparent,
  },
  lastUpdatedTextStyle: {
    color: color.palette.darkGrey999,
    flex: 1,
  },
  locationIconViewStyles: {
    alignItems: "center",
    borderLeftColor: color.palette.lightGrey,
    borderLeftWidth: 1,
    height: 44,
    justifyContent: "center",
  },
  noInternetOverlayStyle: {
    backgroundColor: color.palette.transparent,
    paddingVertical: 40,
  },
  parentContainerStyleForFlightResult: {
    backgroundColor: color.palette.lightestGrey,
    flex: 1,
    marginBottom: 0,
    paddingBottom: 0,
  },
  positionStyle: {
    bottom: 30,
  },
  resultStyles: {
    color: color.palette.almostBlackGrey,
    textAlign: "left",
  },
  searchBoxContainer: {
    borderLeftColor: color.palette.lightGrey,
    borderLeftWidth: 0.5,
    flex: 1.11,
    flexDirection: "row",
    paddingLeft: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  toastButtonStyle: {
    ...presets.textLink,
    alignItems: "flex-end",
    color: color.palette.lightBlue,
    fontWeight: "normal",
  },
  toastStyle: {
    alignItems: "flex-start",
    backgroundColor: color.palette.black,
    borderRadius: 8,
    height: 60,
    marginBottom: 20,
    width: "95%",
  },
  toastTextStyle: {
    ...presets.bodyTextRegular,
    color: color.palette.whiteGrey,
    width: "80%",
  },
})

export const arrivalStyles = StyleSheet.create({
  badgeViewStyle: {
    alignSelf: "flex-end",
    position: "absolute",
    right: -3,
    top: -3,
    zIndex: 1,
  },
  containerFilter: {
    paddingHorizontal: 24,
  },
  contentContainerStyle: {
    flex: 1,
  },
  emptyView: {
    height: 50,
    justifyContent: "center",
  },
  errorContainerStyles: {
    backgroundColor: color.palette.lightestGrey,
    flex: 1,
  },
  errorOverlayStyle: {
    height: "80%",
    justifyContent: "flex-start",
    paddingVertical: 30,
    width: "100%",
  },
  fabArrowStyle: {
    position: "absolute",
    zIndex: 1,
  },
  fabContainerViewStyle: {
    alignItems: "center",
    alignSelf: "flex-end",
    bottom: 40,
    justifyContent: "center",
    position: "absolute",
    right: 12,
  },
  fabTouchViewStyle: {
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "flex-end",
  },
  feedBackToastStyle: {
    bottom: 24,
    paddingHorizontal: 16,
    width: "100%",
  },
  flatListHeaderStyles: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    marginVertical: 12,
    paddingLeft: 24,
    paddingRight: 40,
  },
  iconViewStyles: {
    padding: 2,
  },
  lastUpdatedTextStyle: {
    color: color.palette.darkGrey999,
    flex: 1,
  },
  noInternetOverlayStyle: {
    backgroundColor: color.palette.transparent,
    paddingVertical: 40,
  },
  parentContainerStyleForFlightResult: {
    backgroundColor: color.palette.lightestGrey,
    flex: 1,
    marginBottom: 0,
    paddingBottom: 0,
  },
  positionStyle: {
    bottom: 30,
  },
  resultStyles: {
    color: color.palette.almostBlackGrey,
    textAlign: "left",
  },
  scanIconContainer: {
    position: "absolute",
    right: 13,
  },
  scanIconStyles: {
    tintColor: color.palette.lightPurple,
  },
  searchBarContainer: {
    alignItems: "center",
    flexDirection: "row",
    marginHorizontal: 24,
    marginTop: 24,
  },
  searchStyle: {
    width: "100%",
  },
  toastButtonStyle: {
    ...presets.textLink,
    alignItems: "flex-end",
    color: color.palette.lightBlue,
    fontWeight: "normal",
  },
  toastStyle: {
    alignItems: "flex-start",
    backgroundColor: color.palette.black,
    borderRadius: 8,
    height: 60,
    marginBottom: 20,
    width: "95%",
  },
  toastTextStyle: {
    ...presets.bodyTextRegular,
    color: color.palette.whiteGrey,
    width: "80%",
  },
})

export const commonComponentStyles = StyleSheet.create({
  emptyComponentStyles: {
    flex: 1,
    justifyContent: "center",
  },
  lottieLoading: {
    alignItems: "center",
    alignSelf: "center",
    backgroundColor: color.palette.transparent,
    marginLeft: 5,
    marginTop: 20,
    width: "70%",
  },
  lottieLoadingBackground: {
    backgroundColor: color.palette.whiteColorOpacity,
    bottom: 0,
    flex: 1,
    position: "absolute",
    width: "100%",
  },
  modalCalendarStyles: { margin: 0 },
  textEmptyComponentStyles: { marginTop: 24, paddingHorizontal: 24 },
  noFlightListingStyle: {
    marginHorizontal: 24,
    marginBottom: 10,
    textAlign: "center",
    paddingBottom: 15
  },
  flatListItemStyle: {
    paddingBottom: 20,
    alignItems: "center"
  },
  sectionListWrapper: {
    backgroundColor: color.palette.lightestGrey,
  }
})
