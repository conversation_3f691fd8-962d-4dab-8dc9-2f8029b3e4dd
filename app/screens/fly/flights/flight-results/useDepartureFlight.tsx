import { env } from "app/config/env-params"
import {
  deleteMyTravelFlightDetail,
  getFlightsV2,
  getFlyEarlierListQuery,
  myTravelInsertFlight,
  searchFlights,
} from "app/models/queries"
import { FlyCreators, FlySelectors, mergeToDateGroup } from "app/redux/flyRedux"
import { store } from "app/redux/store"
import { FlyList } from "app/redux/types/fly/fly"
import {
  formatResponseMyTravelInsertFlight,
  formatResponseRemoveMyTravelFlight,
  getQueryFilter,
  handleScheduledDateForEarlierFly,
  handleScheduledDateForFly,
  handleScheduledTimeForEarlierFly,
  handleScheduledTimeForFly,
} from "app/sagas/flySaga"
import { FB_EVENT_NAME } from "app/services/facebook/event-name"
import {
  ANALYTICS_LOG_EVENT_NAME,
  FE_LOG_PREFIX,
  analyticsLogEvent,
  convertStringValue,
  dtACtionLogEvent,
  dtBizEvent,
  dtManualActionEvent,
} from "app/services/firebase/analytics"
import { DateFormats, flyModuleUpdatedTime, getDateSingapore } from "app/utils/date-time/date-time"
import { graphqlOperation } from "aws-amplify"
import moment, { Moment } from "moment"
import momentTz from "moment-timezone"
import { useEffect, useMemo, useRef, useState } from "react"
import DeviceInfo from "react-native-device-info"
import { AppEventsLogger } from "react-native-fbsdk-next"
import { useDispatch, useSelector } from "react-redux"
import requestApi from "../../../../services/api/request"
import { FlightDirection } from "../flight-props"
import { isEmpty } from "lodash"
import { useFlightSaveErrorHandling } from "app/hooks/useFlightSaveErrorHandling"
import { useFlightGamification } from "app/hooks/useFlightGamification"
import { MytravelCreators, MytravelSelectors } from "app/redux/mytravelRedux"
import { AdobeTagName, trackAction } from "app/services/adobe"

const SCREEN_NAME = "DepartureResultScreen__"
interface IGetFlightListRequest {
  direction: string
  filterDate: Moment
  filters: string[]
  isFilter: boolean
  isLoadFlightAfter24h: boolean
  isLoadMore?: boolean
  filterAirline?: string
  filterCityAirport?: string
}

interface ISearchFlightListRequest {
  direction: string
  filterDate: Moment
  filterTerminal: string[]
  keyword: string
  pageSize?: string
  pageNumber?: string
  isLoadMore?: boolean
  filterAirline?: string
  filterCityAirport?: string
}

const PAGE_SIZE = 30
export const useDepatureFlight = () => {
  const [sectionList, setSectionList] = useState([])

  const [isLoading, setLoading] = useState(false)
  const [searchPageNumber, setSearchPageNumber] = useState(1)
  const [searchPageTotal, setSearchPageTotal] = useState(0)
  const searchMoreEnabled = useMemo(() => {
    return searchPageNumber * PAGE_SIZE < searchPageTotal
  }, [searchPageNumber, searchPageTotal])

  const [lastUpdatedTime, setLastUpdatedTime] = useState(flyModuleUpdatedTime())
  const [nextToken, setNextToken] = useState("")
  const [isEndLoadMore, setEndLoadMore] = useState(false)
  const [isNetworkError, setNetworkError] = useState(false)
  const [previousToken, setPreviousToken] = useState("")
  const [isEndEarlierFlights, setIsEndEarlierFlights] = useState(false)
  const [isLoadingEarlierFlights, setIsLoadingEarlierFlights] = useState(false)
  const [loadedEarlierFlights, setLoadedEarlierFlights] = useState(false)

  const loadingRef = useRef(false)
  const dispatch = useDispatch()

  const { flightListingPayload: flyData } = useSelector(FlySelectors.flightListingPayload)
  const myTravelFlightsPayload = useSelector(MytravelSelectors.myTravelFlightsPayload)
  const { rewardGamificationSavedFlights } = useFlightGamification()

  useFlightSaveErrorHandling()

  useEffect(() => {
    if (flyData?.payload) {
      const temp = flyData?.payload?.map((item) => {
        return {
          title: item.date,
          data: item.flightListingData,
        }
      })
      setSectionList(temp || [])
    } else {
      setSectionList([])
    }
  }, [flyData])

  const resetSearchData = () => {
    setSearchPageNumber(1)
    setSearchPageTotal(0)
  }

  const resetLoadListData = () => {
    setNextToken("")
  }

  const getFlyDepartureList = async (request: IGetFlightListRequest) => {
    const {
      direction,
      filterDate,
      filters,
      isFilter,
      isLoadFlightAfter24h,
      isLoadMore,
      filterAirline,
      filterCityAirport,
    } = request
    try {
      if (loadingRef.current || isLoading) {
        return
      }
      loadingRef.current = true
      setLoading(true)
      const scheduledDate = getDateSingapore(filterDate)

      const body = {
        direction,
        page_size: 30,
        ...(isLoadMore ? { next_token: nextToken } : {}),
        scheduled_date: handleScheduledDateForFly(isFilter, isLoadFlightAfter24h, scheduledDate),
        scheduled_time: handleScheduledTimeForFly(isLoadFlightAfter24h),
        ...getQueryFilter(filters),
        airline: filterAirline,
        airport: filterCityAirport,
      }

      const response = await requestApi({
        url: env()?.APPSYNC_GRAPHQL_URL,
        method: "post",
        data: graphqlOperation(getFlightsV2, { ...body }),
        parameters: {},
        headers: {
          "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
        },
      })
      const action = {
        payload: response.data,
      }

      const convertedData: any = new FlyList().success(
        action,
        store.getState().flyReducer?.flyCodesPayload,
        store.getState().mytravelReducer?.myTravelFlightsPayload,
      )

      let tempFlightList = []

      if (isLoadMore) {
        const mergeList = {
          data: [...flyData?.payload, ...convertedData.data],
        }
        tempFlightList = mergeToDateGroup(mergeList)
      } else {
        tempFlightList = convertedData?.data
      }
      setNetworkError(false)
      dispatch(FlyCreators.flyDepartureListSuccessV2(tempFlightList))
      setLoading(false)
      setNextToken(action.payload.data?.getFlights?.next_token)
      setEndLoadMore((_state) => {
        if (action.payload.data?.getFlights?.next_token) {
          return false
        } else {
          return true
        }
      })
      if (!isLoadMore) {
        setIsEndEarlierFlights(false)
        setLoadedEarlierFlights(false)
      }
    } catch (err) {
      setNetworkError(true)
    } finally {
      setLoading(false)
      loadingRef.current = false
      setLastUpdatedTime(flyModuleUpdatedTime())
      resetSearchData()
    }
  }

  const searchDepartureFlight = async (request: ISearchFlightListRequest) => {
    const {
      direction,
      filterDate,
      filterTerminal,
      keyword,
      pageSize = 30,
      isLoadMore = false,
      filterAirline,
      filterCityAirport,
    } = request
    try {
      if (loadingRef.current || isLoading) {
        return
      }
      loadingRef.current = true
      setLoading(true)
      const scheduledDate = moment(filterDate).format(DateFormats.YearMonthDay)

      const bodyRequest = {
        text: keyword,
        filter: {
          direction,
          scheduled_date: scheduledDate,
          ...getQueryFilter(filterTerminal),
          airline: filterAirline,
          airport: filterCityAirport,
        },
        page_number: isLoadMore ? searchPageNumber + 1 : 1,
        page_size: pageSize,
      }
      const response = await requestApi({
        url: env()?.APPSYNC_GRAPHQL_URL,
        method: "post",
        data: graphqlOperation(searchFlights, bodyRequest),
        parameters: {},
        headers: {
          "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
        },
      })
      const mappedResponse = {
        data: {
          getFlights: {
            flights: response.data.data.flights.items,
          },
        },
      }
      const action = {
        payload: mappedResponse,
      }

      const convertedData: any = new FlyList().success(
        action,
        store.getState().flyReducer?.flyCodesPayload,
        store.getState().mytravelReducer?.myTravelFlightsPayload,
      )

      let tempFlightList = []
      if (isLoadMore) {
        const mergeList = {
          data: [...flyData?.payload, ...convertedData.data],
        }
        tempFlightList = mergeToDateGroup(mergeList)
      } else {
        tempFlightList = convertedData?.data
      }
      setNetworkError(false)
      dispatch(FlyCreators.flyDepartureListSuccessV2(tempFlightList))
      setLoading(false)
      setSearchPageNumber(response.data.data.flights.page_number)
      setSearchPageTotal(response.data.data.flights.total)
    } catch (err) {
      setNetworkError(true)
    } finally {
      loadingRef.current = false
      setLoading(false)
      setLastUpdatedTime(flyModuleUpdatedTime())
      resetLoadListData()
    }
  }

  const myTravelInsertFlightQuery = async (
    input,
    payload,
    connectingFlightPayload,
    successCallback: (isFirstFlight: boolean) => void,
    failureCallback: () => void,
  ) => {
    let connectingFlight = {}
    if (
      connectingFlightPayload.isConnecting &&
      !isEmpty(connectingFlightPayload.flightConnecting)
    ) {
      connectingFlight = {
        flightDirection: connectingFlightPayload?.flightConnecting?.direction,
        flightNo: connectingFlightPayload?.flightConnecting?.flightNumber,
        iataAirportCode: connectingFlightPayload?.flightConnecting?.departingCode,
        scheduledDate: connectingFlightPayload?.flightConnecting?.flightDate,
        scheduledTime:
          connectingFlightPayload?.flightConnecting?.timeOfFlight ||
          connectingFlightPayload?.flightConnecting?.scheduledTime,
        terminal: connectingFlightPayload?.flightConnecting?.terminal,
      }
    }
    const query = {
      input: {
        deviceId: DeviceInfo.getUniqueIdSync(),
        flightDirection: input?.flightDirection,
        ocidEmail: input?.enterpriseUserId,
        flightNo: input?.flightNumber,
        iataAirportCode:
          payload?.item?.direction === FlightDirection.departure
            ? payload?.item?.destinationCode
            : payload?.item?.departingCode,
        isPassenger: input?.flightPax,
        scheduledDate: payload?.item?.flightDate,
        scheduledTime: payload?.item?.timeOfFlight || payload?.item?.scheduledTime,
        odtt: connectingFlightPayload?.isConnecting ? "TT" : "OD",
        ...(!isEmpty(connectingFlight) ? { connectingFlight: connectingFlight } : {}),
        flightStatus: payload?.item?.flightStatus,
      },
    }
    const dtAction = dtManualActionEvent(`${FE_LOG_PREFIX}App__flight-departure-listing-save`)
    try {
      dtAction.reportStringValue(
        "flight-departure-listing-save-press-flightNumber",
        `${query.input.flightNo}`,
      )
      dtAction.reportStringValue(
        "flight-departure-listing-save-press-scheduledDate",
        `${query.input.scheduledDate}`,
      )
      dtAction.reportStringValue(
        "flight-departure-listing-save-press-flightDirection",
        `${query.input.flightDirection}`,
      )
      dtAction.reportStringValue(
        "flight-departure-listing-save-press-isPassenger",
        `${query.input.isPassenger}`,
      )
      const response = await requestApi({
        url: env()?.APPSYNC_GRAPHQL_URL,
        method: "post",
        data: graphqlOperation(myTravelInsertFlight, query),
        parameters: {},
        headers: {
          "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
        },
      })
      if (response.data.errors) {
        dtAction.reportStringValue(
          "flight-departure-listing-save-error-response",
          convertStringValue(JSON.stringify(response.data.errors)),
        )
        dispatch(MytravelCreators.flyMyTravelInsertFlightFailure(payload))
        failureCallback()
        return
      }
      dispatch(
        MytravelCreators.flyMyTravelInsertFlightSuccess(
          formatResponseMyTravelInsertFlight(response),
          {
            ...payload,
            isPassenger: input?.flightPax,
          },
        ),
      )
      dtAction.reportStringValue("flight-departure-listing-save-success", "success")
      // get gamification reward game chance
      if (response?.data?.data?.saveFlight?.eligibleForGameChance) {
        const {
          flightNumber,
          actualTimestamp,
          displayTimestamp,
          scheduledDate,
          scheduledTime,
          timeOfFlight,
        } = payload?.item
        const priorityTime =
          actualTimestamp || displayTimestamp || `${scheduledDate} ${scheduledTime || timeOfFlight}`
        const formatedScheduledTime = momentTz(priorityTime).format("YYYY-MM-DD HH:mm").toString()
        const currentTimeToUTC = momentTz()
          .tz("Asia/Singapore")
          .format("YYYY-MM-DD HH:mm")
          .toString()
        const gameChanceInput = {
          flightNumber: flightNumber,
          flightDatetime: formatedScheduledTime,
          saveTimestamp: currentTimeToUTC,
        }
        rewardGamificationSavedFlights(gameChanceInput)
      }

      const isFirstSaveFlight = myTravelFlightsPayload?.getMyTravelFlightDetails?.length == 0
      successCallback(isFirstSaveFlight)
      analyticsLogEvent(ANALYTICS_LOG_EVENT_NAME.SAVEFLIGHT_DEPARTURE)
      dtACtionLogEvent(ANALYTICS_LOG_EVENT_NAME.SAVEFLIGHT_DEPARTURE)
      dtBizEvent(
        SCREEN_NAME,
        ANALYTICS_LOG_EVENT_NAME.SAVEFLIGHT_DEPARTURE,
        "TAP-ON-SAVE-DEP-FLIGHT",
        {},
      )
      AppEventsLogger.logEvent(FB_EVENT_NAME.SAVE_FLIGHT_DEPARTURE, null)
    } catch (err) {
      dtAction.reportStringValue(
        "flight-departure-listing-save-error",
        convertStringValue(err?.message),
      )
      dispatch(MytravelCreators.flyMyTravelInsertFlightFailure(payload))
      failureCallback()
    } finally {
      dtAction.leaveAction()
    }
  }

  const removeSavedFlight = async (
    payload,
    successCallback: () => void,
    failureCallback: () => void,
  ) => {
    const query = {
      input: {
        flightDirection: payload?.item?.flightDirection || payload?.item?.direction,
        flightNo: payload?.item?.flightNumber,
        scheduledDate: payload?.item?.scheduledDate || payload?.item?.flightDate,
      },
    }
    const dtAction = dtManualActionEvent(`${FE_LOG_PREFIX}App__flight-departure-listing-unsave`)
    try {
      dtAction.reportStringValue(
        "flight-departure-listing-unsave-query-flightNo",
        `${query.input.flightNo}`,
      )
      dtAction.reportStringValue(
        "flight-departure-listing-unsave-query-scheduledDate",
        `${query.input.scheduledDate}`,
      )
      dtAction.reportStringValue(
        "flight-departure-listing-unsave-query-flightDirection",
        `${query.input.flightDirection}`,
      )
      const response = await requestApi({
        url: env()?.APPSYNC_GRAPHQL_URL,
        method: "post",
        data: graphqlOperation(deleteMyTravelFlightDetail, query),
        parameters: {},
        headers: {
          "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
        },
      })
      if (response.data.errors) {
        dtAction.reportStringValue(
          "flight-departure-listing-unsave-error-response",
          convertStringValue(JSON.stringify(response.data.errors)),
        )
        dispatch(MytravelCreators.flyMyTravelRemoveFlightFailure(payload))
        failureCallback()
        return
      }
      dispatch(
        MytravelCreators.flyMyTravelRemoveFlightSuccess(
          formatResponseRemoveMyTravelFlight(response?.data),
          payload,
        ),
      )
      dtAction.reportStringValue("flight-departure-listing-unsave-success", "success")
      successCallback()
    } catch (err) {
      dtAction.reportStringValue(
        "flight-departure-listing-unsave-error",
        convertStringValue(err?.message),
      )
      dispatch(MytravelCreators.flyMyTravelRemoveFlightFailure(payload))
      failureCallback()
    } finally {
      dtAction.leaveAction()
    }
  }

  const apiCallJob = useRef(null)
  const isFocusedRef = useRef(true)

  const refreshInterval = env()?.FLIGHT_REFRESH_INTERVAL
  const startLoopApiCall = (callback: () => void) => {
    cancelLoopApiJob()
    apiCallJob.current = setTimeout(() => {
      if (isFocusedRef.current) {
        callback()
        startLoopApiCall(callback)
      }
    }, refreshInterval)
  }

  const cancelLoopApiJob = () => {
    clearTimeout(apiCallJob.current)
  }

  const getEarlierFlights = async (request: IGetFlightListRequest) => {
    const {
      direction,
      filterDate,
      filters,
      isLoadFlightAfter24h,
      filterAirline,
      filterCityAirport,
    } = request

    trackAction(AdobeTagName.CAppFlyFlightListEarlierFlights, {
      [AdobeTagName.CAppFlyFlightListEarlierFlights]: "1",
    })

    if (!isEmpty(sectionList) && !isEndEarlierFlights) {
      try {
        setIsLoadingEarlierFlights(true)

        let scheduledDate = getDateSingapore(filterDate)
        const currentDate = moment().format(DateFormats.YearMonthDay)
        const isPast = new Date(scheduledDate) < new Date(currentDate)
        if (isPast) {
          scheduledDate = currentDate
        }
        const body = {
          direction,
          next_token: previousToken,
          prev: "true",
          scheduled_date: handleScheduledDateForEarlierFly(isLoadFlightAfter24h, scheduledDate),
          scheduled_time: handleScheduledTimeForEarlierFly(isLoadFlightAfter24h),
          page_size: "5",
          ...getQueryFilter(filters),
          airline: filterAirline,
          airport: filterCityAirport,
        }

        const response = await requestApi({
          url: env()?.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: graphqlOperation(getFlyEarlierListQuery, { ...body }),
          parameters: {},
          headers: {
            "x-api-key": env()?.APPSYNC_GRAPHQL_API_KEY,
          },
        })

        if (response?.data?.data?.getFlights) {
          const action = {
            payload: response?.data,
          }

          const convertedData: any = new FlyList().success(
            action,
            store.getState().flyReducer?.flyCodesPayload,
            store.getState().mytravelReducer?.myTravelFlightsPayload,
          )

          let tempFlightList = []
          const mergeList = {
            data: [...convertedData.data, ...flyData?.payload],
          }
          tempFlightList = mergeToDateGroup(mergeList)

          dispatch(FlyCreators.flyDepartureListSuccessV2(tempFlightList))

          setPreviousToken(response?.data?.data?.getFlights?.next_token)
          setIsEndEarlierFlights(() => {
            if (response?.data?.data?.getFlights?.next_token) {
              return false
            } else {
              return true
            }
          })
          setLoadedEarlierFlights(true)
        }
      } finally {
        setIsLoadingEarlierFlights(false)
      }
    }
  }

  useEffect(() => {
    return cancelLoopApiJob
  }, [])

  return {
    sectionList,
    setSectionList,
    isLoading,
    lastUpdatedTime,
    isFocusedRef,
    isEndLoadMore,
    searchMoreEnabled,
    isNetworkError,
    startLoopApiCall,
    cancelLoopApiJob,
    getFlyDepartureList,
    searchDepartureFlight,
    setNetworkError,
    myTravelInsertFlightQuery,
    removeSavedFlight,
    getEarlierFlights,
    isLoadingEarlierFlights,
    isEndEarlierFlights,
    loadedEarlierFlights,
  }
}
