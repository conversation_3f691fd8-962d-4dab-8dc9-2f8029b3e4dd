import { StyleSheet, Dimensions, Platform } from "react-native"
import { color, typography } from "app/theme"
import { presets } from "app/elements/text"

const { width } = Dimensions.get("window")
export const BG_HEIGHT = width * (160 / 375)

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: color.palette.almostWhiteGrey,
  },
  bgWrap: {
    position: "absolute",
    top: 0,
    left: 0,
    width: width,
    height: BG_HEIGHT,
    zIndex: 1,
  },
  headerSafeArea: {
    position: "relative",
    top: 0,
    left: 0,
    width: width,
    zIndex: 3,
    flex: 1,
  },
  headerRow: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    height: 32,
    paddingHorizontal: 16,
    marginTop: Platform.OS === "android" ? 18 : 0,
  },
  headerTitle: {
    flex: 1,
    textAlign: "center",
    color: color.palette.whiteGrey,
    fontSize: 18,
    fontWeight: Platform.select({ ios: "700", android: "bold" }),
    fontFamily: typography.bold,
    lineHeight: 22,
    letterSpacing: 0,
  },
  realtimeCard: {
    marginBottom: 14,
  },
  liveText: {
    color: color.palette.whiteGrey,
    textAlign: "center",
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    fontFamily: typography.medium,
    fontSize: 11,
    lineHeight: 14,
    letterSpacing: 0,
  },
  realtimeTitle: {
    fontWeight: Platform.select({ ios: "700", android: "bold" }),
    fontFamily: typography.bold,
    fontSize: 14,
    color: "#222",
  },
  flightContainer: {
    flex: 1,
  },
})

export const tabScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: color.palette.almostWhiteGrey,
  },
  lottieLoading: {
    alignItems: "center",
    alignSelf: "center",
    backgroundColor: color.palette.transparent,
    marginLeft: 10,
    width: "70%",
    height: Dimensions.get("window").height,
  },
  lottieLoadingBackground: {
    backgroundColor: color.palette.whiteColorOpacity,
    bottom: 0,
    left: 0,
    position: "absolute",
    right: 0,
    top: 0,
  },
  listContainer: { backgroundColor: color.palette.almostWhiteGrey },
  sectionContainer: { paddingHorizontal: 20, marginBottom: 16 },
  filterContainer: {
    paddingTop: 20,
  },
  feedBackToastStyle: {
    bottom: 24,
    paddingHorizontal: 16,
    width: "100%",
  },
  toastButtonStyle: {
    ...presets.textLink,
    alignItems: "flex-end",
    color: color.palette.lightBlue,
    fontWeight: "normal",
  },
  toastTextStyle: {
    ...presets.bodyTextRegular,
    color: color.palette.whiteGrey,
    width: "80%",
  },
  noEarlierFlights: {marginBottom: 20, marginHorizontal: 20},
})
