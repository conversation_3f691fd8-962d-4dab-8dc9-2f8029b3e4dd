export interface FacilityServiceApiItem {
  contentId: string
  image?: string
  title: string
  locationDisplayText?: string
  navigation?: { type: string; value: string }
  cagOverride?: boolean
  sequenceNumber?: string
  startDate?: string
  expiryDate?: string
  tagName?: string[]
  customerEligibility?: Array<{ tagTitle: string; tagName: string; filterType: string; sequenceNumber: number; childTags: any[] }>
  userGroup?: Array<{ tagTitle: string; tagName: string; filterType: string; sequenceNumber: number; childTags: any[] }>
  flow?: Array<{ tagTitle: string; tagName: string; filterType: string; sequenceNumber: number; childTags: any[] }>
  locationDescription?: Array<{ tagTitle: string; tagName: string; filterType: string; sequenceNumber: number; childTags: any[] }>
  location?: Array<{ tagTitle: string; tagName: string; filterType: string; sequenceNumber: number; childTags: any[] }>
  area?: Array<{ tagTitle: string; tagName: string; filterType: string; sequenceNumber: number; childTags: any[] }>
  redirect?: any
}