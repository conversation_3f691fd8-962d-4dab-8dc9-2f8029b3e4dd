import { FacilityServiceApiItem } from "./types"
import { IFacilityServiceItem } from "./components/facility-service-item"
import { env } from "app/config/env-params"

export const mapApiItemToFacilityServiceItem = (
  item: FacilityServiceApiItem,
): IFacilityServiceItem => {
  return {
    id: item.contentId,
    title: item.title,
    locationDisplayText: item.locationDisplayText,
    areaList: item.area?.map((a) => a.tagTitle) || [],
    image: item.image ? env()?.AEM_URL + item.image : undefined,
    navigation: item.navigation
      ? { type: item.navigation.type, value: item.navigation.value }
      : undefined,
  }
}
