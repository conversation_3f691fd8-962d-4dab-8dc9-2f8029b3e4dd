import { Dimensions } from "react-native"

export const SCREEN_WIDTH = Dimensions.get("window").width
export const HEADER_BACKGROUND_WIDTH = 375
export const HEADER_BACKGROUND_HEIGHT = 258
export const SCALE_FACTOR = SCREEN_WIDTH / HEADER_BACKGROUND_WIDTH
export const HEADER_BACKGROUND_HEIGHT_SCALED = HEADER_BACKGROUND_HEIGHT * SCALE_FACTOR

export const HEADER_TOP = 0
export const HEADER_HEIGHT = 100
export const HEADER_BOTTOM = HEADER_TOP + HEADER_HEIGHT
export const FILTER_BAR_TOP = HEADER_BOTTOM + 15
export const FILTER_BAR_HEIGHT = 54
export const FILTER_BAR_BOTTOM = FILTER_BAR_TOP + FILTER_BAR_HEIGHT
export const CONTENT_TOP = HEADER_BOTTOM + 15