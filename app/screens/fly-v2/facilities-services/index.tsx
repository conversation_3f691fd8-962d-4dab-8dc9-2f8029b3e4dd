import React from "react"
import { View, StatusBar, StyleSheet, Image } from "react-native"
import { useNavigation } from "@react-navigation/native"
import { StackNavigationProp } from "@react-navigation/stack"
import Animated, {
  useSharedValue,
  useAnimatedScrollHandler,
  interpolate,
  useAnimatedStyle,
  Extrapolation,
} from "react-native-reanimated"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import type { PrimaryParamList } from "app/navigators/main-navigator"
import { Text } from "app/elements/text"
import { color } from "app/theme/color"
import { NavigationConstants } from "app/utils/constants"
import { useFacilitiesServices } from "./useFacilitiesServices"
import { FacilityServiceItem, FacilityServiceItemSkeleton } from "./components/facility-service-item"
import { AnimatedHeader } from "./components/animated-header"
import { AnimatedFilterBar } from "./components/animated-filter-bar"
import {
  HEADER_HEIGHT,
  FILTER_BAR_HEIGHT,
  SCREEN_WIDTH,
  HEADER_BACKGROUND_HEIGHT_SCALED,
} from "./constants"

export const FacilitiesServices = () => {
  const navigation = useNavigation<StackNavigationProp<PrimaryParamList, "facilitiesServices">>()
  const { facilities, loading, error } = useFacilitiesServices()
  const insets = useSafeAreaInsets()

  const scrollY = useSharedValue(0)
  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y
    },
  })
  const borderRadiusStyle = useAnimatedStyle(() => ({
    borderTopLeftRadius: interpolate(scrollY.value, [0, 15], [20, 0], Extrapolation.CLAMP),
    borderTopRightRadius: interpolate(scrollY.value, [0, 15], [20, 0], Extrapolation.CLAMP),
  }))

  const handleGoBack = () => {
    navigation?.goBack()
  }

  const handleSearch = () => {
    // @ts-ignore
    navigation.navigate(NavigationConstants.search, {
      focusTextInput: true,
    })
  }

  function renderContent() {
    if (loading) {
      return (
        <View style={styles.listItemsContainer}>
          {Array.from({ length: 5 }).map((_, index) => (
            <FacilityServiceItemSkeleton key={index} />
          ))}
        </View>
      )
    }
    if (error) {
      return (
        <View style={{ flex: 1, justifyContent: "center", alignItems: "center", padding: 24 }}>
          <Text text={error} style={{ color: "red" }} />
        </View>
      )
    }
    return (
      <View style={styles.listItemsContainer}>
        {facilities.map((item) => (
          <FacilityServiceItem key={item.id} item={item} />
        ))}
      </View>
    )
  }

  return (
    <View style={styles.screenContainer}>
      <StatusBar barStyle="light-content" translucent backgroundColor="transparent" />
      <Image
        source={require("./facilities-services-bg.jpg")}
        style={styles.fixedBackground}
        resizeMode="contain"
      />
      <Animated.ScrollView
        showsVerticalScrollIndicator={false}
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        contentContainerStyle={{ paddingBottom: insets.bottom }}
      >
        <View style={styles.pseduoHeader} />
        <Animated.View style={[styles.contentContainer, borderRadiusStyle]}>
          <View style={styles.pseduoFilterBar} />
          {renderContent()}
        </Animated.View>
      </Animated.ScrollView>
      <AnimatedFilterBar scrollY={scrollY} onSearchPress={handleSearch} />
      <AnimatedHeader scrollY={scrollY} onBackPress={handleGoBack} />
    </View>
  )
}

const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: color.palette.whiteGrey,
  },
  fixedBackground: {
    width: SCREEN_WIDTH,
    height: HEADER_BACKGROUND_HEIGHT_SCALED,
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  pseduoHeader: {
    height: HEADER_HEIGHT,
    marginBottom: 15,
  },
  pseduoFilterBar: {
    height: FILTER_BAR_HEIGHT,
    marginBottom: 16,
  },
  contentContainer: {
    flex: 1,
    backgroundColor: color.palette.whiteGrey,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: "hidden",
    paddingBottom: 20,
  },
  listItemsContainer: {
    flex: 1,
    gap: 20,
  },
})
