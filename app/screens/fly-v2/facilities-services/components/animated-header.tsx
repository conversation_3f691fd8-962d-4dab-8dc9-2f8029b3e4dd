import React, { <PERSON> } from "react"
import { View, TouchableOpacity, StyleSheet } from "react-native"
import { newPresets, Text } from "app/elements/text"
import { color } from "app/theme/color"
import { ArrowLeftV2 } from "assets/icons"
import Animated, {
  useAnimatedStyle,
  interpolate,
  SharedValue,
  Extrapolation,
} from "react-native-reanimated"
import { HEADER_HEIGHT } from "../constants"

interface AnimatedHeaderProps {
  scrollY: SharedValue<number>
  onBackPress: () => void
}

export const AnimatedHeader: FC<AnimatedHeaderProps> = (props) => {
  const { scrollY, onBackPress } = props

  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: interpolate(scrollY.value, [0, 15], [0, 1], Extrapolation.CLAMP),
  }))
  return (
    <>
      <View style={styles.staticHeaderContainer}>
        <View style={styles.headerRow}>
          <Text tx="facilitiesServices.title" style={styles.staticHeaderTitle} />
          <TouchableOpacity onPress={onBackPress}>
            <ArrowLeftV2 width={24} height={24} color={color.palette.whiteGrey} />
          </TouchableOpacity>
        </View>
      </View>

      <Animated.View style={[styles.fadingHeaderContainer, headerAnimatedStyle]}>
        <View style={styles.headerRow}>
          <Text tx="facilitiesServices.title" style={styles.fadingHeaderTitle} />
          <TouchableOpacity onPress={onBackPress}>
            <ArrowLeftV2 width={24} height={24} color={color.palette.darkestGrey} />
          </TouchableOpacity>
        </View>
      </Animated.View>
    </>
  )
}

const styles = StyleSheet.create({
  staticHeaderContainer: {
    height: HEADER_HEIGHT,
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
  },

  fadingHeaderContainer: {
    height: 100,
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,

    backgroundColor: "white",
    shadowColor: "#121212",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
  },
  headerRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    marginTop: 61,
  },
  staticHeaderTitle: {
    ...newPresets.subTitleBold,
    position: "absolute",
    left: 0,
    right: 0,
    color: color.palette.whiteGrey,
    textAlign: "center",
  },

  fadingHeaderTitle: {
    ...newPresets.subTitleBold,
    position: "absolute",
    left: 0,
    right: 0,
    color: color.palette.darkestGrey,
    textAlign: "center",
  },
})
