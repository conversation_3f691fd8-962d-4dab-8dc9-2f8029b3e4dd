import { color } from "app/theme"
import React from "react"
import { View } from "react-native"
import { StyleSheet } from "react-native"

export const FacilityServiceItemSkeleton = () => (
  <View style={styles.container}>
    <View style={styles.imagePlaceholder} />
    <View style={styles.content}>
      <View style={styles.titlePlaceholder} />
      <View style={styles.locationPlaceholder} />
    </View>
  </View>
)

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "flex-start",
    paddingLeft: 20,
    paddingRight: 40,
    height: 66,
    gap: 16,
  },
  imagePlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: color.palette.lighterGrey,
  },
  content: {
    flex: 1,
    alignSelf: "stretch",
    borderBottomWidth: 1,
    borderBottomColor: color.palette.lighterGrey,
  },
  titlePlaceholder: {
    width: "100%",
    height: 12,
    borderRadius: 4,
    backgroundColor: color.palette.lighterGrey,
    marginBottom: 12,
  },
  locationPlaceholder: {
    width: "50%",
    height: 12,
    borderRadius: 4,
    backgroundColor: color.palette.lighterGrey,
  },
})
