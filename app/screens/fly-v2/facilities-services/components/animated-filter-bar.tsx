import React, { <PERSON> } from "react"
import { View, TouchableOpacity, StyleSheet, ScrollView } from "react-native"
import { color } from "app/theme/color"
import { SearchIconV2 } from "assets/icons"
import Animated, { useAnimatedStyle, SharedValue } from "react-native-reanimated"
import { FILTER_BAR_TOP, HEADER_BOTTOM } from "../constants"
interface AnimatedFilterBarProps {
  scrollY: SharedValue<number>
  onSearchPress: () => void
}

export const AnimatedFilterBar: FC<AnimatedFilterBarProps> = (props) => {
  const { scrollY, onSearchPress } = props
  const filtersAnimatedStyle = useAnimatedStyle(() => {
    // If user scroll down, move the filter bar together with the content
    if (scrollY.value <= 0) {
      return { top: FILTER_BAR_TOP + Math.abs(scrollY.value) }
    }
    // If user scroll up, move the filter bar together with the content
    // and stick if filter bar reach the bottom of header
    return { top: Math.max(HEADER_BOTTOM, FILTER_BAR_TOP - scrollY.value) }
  })
  return (
    <Animated.View style={[styles.filtersContainer, filtersAnimatedStyle]}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 20 }}
      >
        <View style={styles.filtersRow}>
          <TouchableOpacity style={styles.filterIconButton} onPress={onSearchPress}>
            <SearchIconV2 width={16} height={16} color={color.palette.darkestGrey} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </Animated.View>
  )
}

const styles = StyleSheet.create({
  filtersContainer: {
    position: "absolute",
    top: FILTER_BAR_TOP,
    left: 0,
    right: 0,
    height: 54,
    backgroundColor: color.palette.whiteGrey,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  filtersRow: {
    flexDirection: "row",
    gap: 4,
    alignItems: "center",
  },
  filterIconButton: {
    height: 30,
    width: 36,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 99,
    borderWidth: 1,
    borderColor: color.palette.lighterGrey,
  },
})
