import { useEffect, useState } from "react"
import path from "app/services/api/apis.json"
import restApi from "app/services/api/request"
import { env } from "app/config/env-params"
import { FacilityServiceApiItem } from "./types"
import { mapApiItemToFacilityServiceItem } from "./data-mapping"
import type { IFacilityServiceItem } from "./components/facility-service-item"

export function useFacilitiesServices() {
  const [facilities, setFacilities] = useState<IFacilityServiceItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchFacilities = async () => {
      setLoading(true)
      setError(null)
      try {
        const paramsArray = path.getFacilitiesServices.split(" ")
        const method = paramsArray[0] || "GET"
        const url = env()?.AEM_URL + paramsArray[1]
        const response = await restApi({ url, method })
        const items: FacilityServiceApiItem[] = Array.isArray(response?.data?.list)
          ? response?.data?.list
          : []
        setFacilities(items.map(mapApiItemToFacilityServiceItem))
      } catch (err: any) {
        setError(err?.message || "Failed to fetch facilities/services.")
      } finally {
        setLoading(false)
      }
    }
    fetchFacilities()
  }, [])

  return { facilities, loading, error }
}
