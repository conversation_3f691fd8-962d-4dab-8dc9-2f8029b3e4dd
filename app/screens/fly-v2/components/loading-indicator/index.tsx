
import { color } from 'app/theme';
import React from 'react';
import { View, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  Easing,
  interpolate,
} from 'react-native-reanimated';

const Dot = ({ index }) => {
  const progress = useSharedValue(0);

  React.useEffect(() => {
    progress.value = withRepeat(
      withTiming(1, {
        duration: 1000,
        easing: Easing.linear,
      }),
      -1,
      false
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    const inputRange = [0, 0.5, 1];
    const outputRange = [0, -10, 0];

    const phase = (index * 0.2) % 1;

    const shiftedProgress = (progress.value + phase) % 1;

    const translateY = interpolate(
      shiftedProgress,
      inputRange,
      outputRange,
    );

    return {
      transform: [{ translateY }],
    };
  });

  return <Animated.View style={[styles.dot, animatedStyle]} />;
};

const LoadingIndicator = () => {
  return (
    <View style={styles.container}>
      <Dot index={0} />
      <Dot index={1} />
      <Dot index={2} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    gap: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 8,
    backgroundColor: color.palette.lightPurple,
  },
});

export default LoadingIndicator;