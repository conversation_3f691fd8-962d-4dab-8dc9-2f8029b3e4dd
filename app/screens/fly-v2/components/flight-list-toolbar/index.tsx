import React from "react"
import { View, TouchableOpacity } from "react-native"
import { Text } from "app/elements/text/text"
import styles from "./styles"

interface FlightListToolbarProps {
  onGetEarlierFlights: () => void;
  componentName: string;
}

function FlightListToolbar({ onGetEarlierFlights, componentName }: FlightListToolbarProps) {
  return (
    <View>
      {/* Notice */}
      <Text style={styles.notice} tx="flightLanding.flightNotice" />

      <TouchableOpacity
        onPress={onGetEarlierFlights}
        style={styles.loadBtn}
        testID={`${componentName}__ButtonFlightsHandler`}
        accessibilityLabel={`${componentName}__ButtonFlightsHandler`}
      >
        <Text style={styles.loadBtnText} tx="flightLanding.loadEarlierFlights" />
      </TouchableOpacity>
    </View>
  )
}
export default FlightListToolbar
