import NetInfo from "@react-native-community/netinfo"
import { useFocusEffect } from "@react-navigation/native"
import { AlertApp } from "app/components/alert-app/alert-app"
import LoadingAnimation from "app/components/loading-modal/./loading-animation.json"
import { Text } from "app/elements/text"
import AemActions, { AEM_PAGE_NAME, AemSelectors } from "app/redux/aemRedux"
import { FlightListingCreators, FlightListingSelectors } from "app/redux/flightListingRedux"
import { FlySelectors } from "app/redux/flyRedux"
import { MytravelCreators } from "app/redux/mytravelRedux"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { FlightDirection } from "app/screens/fly/flights/flight-props"
import { commonComponentStyles } from "app/screens/fly/flights/flight-results/flight-result-styles"
import { FlyContext } from "app/screens/fly/flights/flight-results/flight-results"
import { useArrivalFlight } from "app/screens/fly/flights/flight-results/useArrivalFlight"
import { SearchFlightsOptions } from "app/screens/search-v2/search-result/search-flights-result"
import Category from "app/screens/search-v2/search-result/search-flights-result/category"
import { FlightItem } from "app/screens/search-v2/search-result/search-flights-result/flight-item"
import { AdobeTagName, trackAction } from "app/services/adobe"
import { simpleCondition } from "app/utils"
import { isEmpty } from "lodash"
import LottieView from "lottie-react-native"
import moment from "moment"
import React, { useCallback, useEffect, useRef } from "react"
import { Alert, SectionList, View } from "react-native"
import Animated, { useSharedValue } from "react-native-reanimated"
import { useDispatch, useSelector } from "react-redux"
import FlightListToolbar from "../components/flight-list-toolbar"
import { tabScreenStyles } from "../flight-listing.styles"
import { useFlightListingContext } from "../contexts/flight-listing-context"
import { translate } from "app/i18n"
import { AlertTypes } from "app/components/alert-app/alert-app.props"
import { trackActionOldFormat } from "app/utils/screen-helper"
import { TOAST_MESSAGE_DURATION } from "app/utils/constants"
import { FeedBackToast, FeedBackToastType } from "app/components/feedback-toast"
import { FlightDirection as BottomSheetFlightDirection } from "app/utils/constants"
import { useModal } from "app/hooks/useModal"
import LoadingIndicator from "../components/loading-indicator"

const COMPONENT_NAME = "ArrivalListingScreen__"

const AnimatedSectionList = Animated.createAnimatedComponent(SectionList)

function ArrivalScreen({ navigation }) {
  // state
  const dispatch = useDispatch()
  const { openSearchBottomSheet, setTerminal, setAirline, setAirport } = useFlightListingContext()

  const flightListingFilter: SearchFlightsOptions = useSelector(
    FlightListingSelectors.flightListingFilter,
  )
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const flightFilterOptions = useSelector(FlySelectors.flightFilterOptions)
  const dataCommonAEM = useSelector(AemSelectors.getMessagesCommon)
  const { isLoadFlightAfter24h } = React.useContext(FlyContext)

  const {
    sectionList,
    isLoading,
    isEndLoadMore,
    isFocusedRef,
    startLoopApiCall,
    getFlyArrivalList,
    removeSavedFlight,
    cancelLoopApiJob,
    getEarlierFlights,
    isLoadingEarlierFlights,
    isEndEarlierFlights,
    loadedEarlierFlights,
  } = useArrivalFlight()
  const { isModalVisible: isFilterModalVisible } = useModal("flightResultCategoryFilter")

  const willBeRefreshed = useSharedValue(true)

  const scrollRef = useRef<SectionList>(null)
  const alertApp = useRef(null)
  const isAfterLogin = useRef(false)
  const filterRef = useRef({
    date: flightListingFilter?.date,
    keyword: flightListingFilter?.keyword,
    terminal: flightListingFilter?.terminal,
    airline: flightListingFilter?.airline,
    airport: flightListingFilter?.airport,
  })
  const toastForRemoveFlight = useRef(null)

  const terminalList = flightFilterOptions?.terminal || []
  const msg58 = dataCommonAEM?.find((e) => e?.code === "MSG58")
  const msg48 = dataCommonAEM?.find((e) => e?.code === "MSG48")
  const msg50 = dataCommonAEM?.find((e) => e?.code === "MSG50")

  // func
  const loadArrivalResults = ({ terminal, airline, airport }: Partial<SearchFlightsOptions>) => {
    getFlyArrivalList({
      direction: FlightDirection.arrival,
      filterDate: moment(),
      filters: simpleCondition({
        condition: terminal?.length === terminalList.length - 1 || isEmpty(terminal),
        ifValue: [],
        elseValue: terminal,
      }),
      isFilter: false,
      isLoadFlightAfter24h: isLoadFlightAfter24h,
      filterAirline: airline ?? "",
      filterCityAirport: airport ?? "",
    })
    startLoopApiCall(handleRefresh)
  }

  const refreshArrivalFlights = ({ terminal, airline, airport }: Partial<SearchFlightsOptions>) => {
    // prevent refresh data while user typing search keyword
    // if (userTyping.current) {
    //   return
    // }
    loadArrivalResults({ terminal, airline, airport })
    dispatch(
      AemActions.getAemConfigData({
        name: AEM_PAGE_NAME.TICKER_BAND_FLIGHT,
        pathName: "getTickerbandFly",
        forceRequest: true,
      }),
    )
  }

  const handleRefresh = async () => {
    const { isConnected } = await NetInfo.fetch()
    if (isConnected) {
      // toastForRefresh?.current?.closeNow()
      if (willBeRefreshed.value && !loadedEarlierFlights) {
        refreshArrivalFlights({
          terminal: filterRef.current.terminal,
          airline: filterRef.current.airline,
          airport: filterRef.current.airport,
        })
        dispatch(MytravelCreators.flyClearInsertFlightPayload())
      }
    }
  }

  const scrollListToTop = () => {
    if (sectionList.length > 0) {
      scrollRef.current?.scrollToLocation({
        animated: true,
        sectionIndex: 0,
        itemIndex: 0,
        viewPosition: 0,
      })
    }
  }

  const onFilter = async ({ terminal, airline, airport }: Partial<SearchFlightsOptions>) => {
    const { isConnected } = await NetInfo.fetch()
    if (isConnected) {
      // if (date) {
      //   trackAction(AdobeTagName.CAppFlightListingFlightListFilter, {
      //     [AdobeTagName.CAppFlightListingFlightListFilter]: moment(date).format("YYYY-MM-DD"),
      //   })
      // }
      // if (keyword) {
      //   trackAction(AdobeTagName.CAppFlightListingFlightListFilter, {
      //     [AdobeTagName.CAppFlightListingFlightListFilter]: `${keyword}`,
      //   })
      // }
      if (terminal) {
        filterRef.current.terminal = terminal
        trackAction(AdobeTagName.CAppFlightListingFlightListFilter, {
          [AdobeTagName.CAppFlightListingFlightListFilter]: `${terminal.join("|")}`,
        })
      }
      if (airline) {
        filterRef.current.airline = airline
      }
      if (airport) {
        filterRef.current.airport = airport
      }
      scrollListToTop()
      refreshArrivalFlights({ terminal, airline, airport })

      // Update the context values so that the search bottom sheet can navigate to SearchResult screen with the correct filters.
      setTerminal(terminal)
      setAirline(airline)
      setAirport(airport)
    }
  }

  const onFlightPress = (item) => {
    const flightDate = item.flightDate
    const flightNumber = item.flightNumber
    const direction = item.direction

    trackAction(AdobeTagName.CAppFlightListingFlightCardClicked, {
      [AdobeTagName.CAppFlightListingFlightCardClicked]: "1",
    })
    trackAction(AdobeTagName.CAppFlightListingViewFlightDetails, {
      [AdobeTagName.CAppFlightListingViewFlightDetails]: `${direction}|${flightDate}|${flightNumber}`,
    })
    //@ts-ignore
    navigation.navigate("flightDetails", {
      payload: {
        item: item,
      },
      direction: FlightDirection.arrival,
    })
  }

  const handleMessage58 = (message, flyItem) => {
    if (message) {
      let status = flyItem?.flightStatus?.toLowerCase()
      if (status?.includes("cancelled")) {
        status = `been ${status}`
      }
      return message
        .replace("<Flight No.>", flyItem?.flightNumber)
        .replace("<departed/landed/been cancelled>", status)
    }
    return message
  }

  const handleMessage48 = (message, number, place) => {
    if (message) {
      return message.replace("<Flight No.>", number).replace("<country>", place)
    }
    return message
  }

  const notAbleToSaveAlert = (flyItem) => {
    const temp = flyItem?.flightStatus?.split(" ")
    const status = temp?.length > 0 ? temp[0] : ""
    const message =
      handleMessage58(msg58?.message, flyItem) ||
      `${translate("flightLanding.flight")} ${flyItem?.flightNumber} ${translate(
        "flightLanding.has",
      )} ${status} ${translate("flightLanding.notSaveMessage")}`
    alertApp?.current?.show({
      title: msg58?.title || translate("flightLanding.alert"),
      description: message,
      labelAccept: msg58?.firstButton || translate("flightLanding.okay"),
      onAccept: () => null,
      type: AlertTypes.ALERT,
    })
  }

  const onRemoveFlight = (payload) => {
    const item = payload?.item
    trackAction(AdobeTagName.CAppFlightListingRemoveFlight, {
      [AdobeTagName.CAppFlightListingRemoveFlight]: "1",
    })
    Alert.alert(
      msg48?.title || translate("flightLanding.areYouSure"),
      msg48?.message
        ? handleMessage48(msg48?.message, item?.flightNumber, item?.destinationPlace)
        : `${translate("flightLanding.removeMessage1")} ${item?.flightNumber} ${translate(
            "flightLanding.to",
          )} ${item?.destinationPlace} ${translate("flightLanding.removeMessage2")}`,
      [
        {
          text: msg48?.firstButton || translate("flightLanding.cancel"),
        },
        {
          text: msg48?.secondButton || translate("flightLanding.remove"),
          style: "cancel",
          onPress: () => {
            removeSavedFlight(
              payload,
              () => {
                const action = "Unsave"
                const flyProfile = "flying"
                const flightStatus = "Successful"
                trackActionOldFormat(AdobeTagName.CAppFlightListingSaveFlight, {
                  pageName: AdobeTagName.CAppFlightListing,
                  flightNumber: item?.flightNumber,
                  flightDirection: FlightDirection.arrival,
                  flightDate: item?.flightDate,
                  flyProfile: flyProfile,
                  action: action,
                  flightStatus: flightStatus,
                })
                toastForRemoveFlight?.current?.show(TOAST_MESSAGE_DURATION)
                dispatch(MytravelCreators.flyClearInsertFlightPayload())
              },
              () => {
                const action = "Unsave"
                const flyProfile = "flying"
                const flightStatus = "Failed"
                trackActionOldFormat(AdobeTagName.CAppFlightListingSaveFlight, {
                  pageName: AdobeTagName.CAppFlightListing,
                  flightNumber: item?.flightNumber,
                  flightDirection: FlightDirection.arrival,
                  flightDate: item?.flightDate,
                  flyProfile: flyProfile,
                  action: action,
                  flightStatus: flightStatus,
                })
              },
            )
          },
        },
      ],
    )
  }

  const onSaveFlight = ({ flight, isSaved, canSaveFlight }) => {
    if (isLoggedIn && isSaved) {
      onRemoveFlight({ item: flight })
    } else {
      if (!canSaveFlight) {
        notAbleToSaveAlert(flight)
      } else {
      }
    }
  }

  const onRenderItem = ({ item }) => {
    return (
      <View style={commonComponentStyles.flatListItemStyle}>
        <FlightItem
          flight={item}
          isLoggedIn={isLoggedIn}
          onPressed={onFlightPress}
          onSaveFlight={onSaveFlight}
        />
      </View>
    )
  }

  const renderSectionHeader = ({ section: { title } }) => {
    return (
      <Text
        preset="caption2Regular"
        text={title?.toUpperCase()}
        style={tabScreenStyles.sectionContainer}
      />
    )
  }

  const handleLoadMore = () => {
    if (isEndLoadMore) {
      return
    }
    getFlyArrivalList({
      direction: FlightDirection.arrival,
      filterDate: filterRef.current?.date ? moment(filterRef.current?.date) : moment(),
      filters: simpleCondition({
        condition:
          filterRef.current?.terminal?.length === terminalList.length - 1 ||
          isEmpty(filterRef.current?.terminal),
        ifValue: [],
        elseValue: filterRef.current?.terminal,
      }),
      isFilter: false,
      isLoadFlightAfter24h: isLoadFlightAfter24h,
      isLoadMore: true,
      filterAirline: filterRef.current?.airline ?? "",
      filterCityAirport: filterRef.current?.airport ?? "",
    })
  }

  const handleSearchPress = () => {
    openSearchBottomSheet(BottomSheetFlightDirection.Arrival)
  }

  const footerListFlight = () => {
    const isEndPage = isEndLoadMore
    if (isEndPage && sectionList.length > 0) {
      return (
        <Text
          tx={"flightLanding.noMoreFlights"}
          preset={"caption1Regular"}
          style={commonComponentStyles.noFlightListingStyle}
        />
      )
    }
    return null
  }

  const onFacetStateChange = (val: boolean) => {
    if (val) {
      cancelLoopApiJob()
    } else {
      startLoopApiCall(handleRefresh)
    }
  }

  const onGetEarlierFlights = () => {
    if (isLoadingEarlierFlights) {
      return
    }
    getEarlierFlights({
      direction: FlightDirection.arrival,
      filterDate: filterRef.current?.date ? moment(filterRef.current?.date) : moment(),
      filters: simpleCondition({
        condition:
          filterRef.current?.terminal?.length === terminalList.length - 1 ||
          isEmpty(filterRef.current?.terminal),
        ifValue: [],
        elseValue: filterRef.current?.terminal,
      }),
      isFilter: false,
      isLoadFlightAfter24h: isLoadFlightAfter24h,
      filterAirline: filterRef.current?.airline ?? "",
      filterCityAirport: filterRef.current?.airport ?? "",
    })
  }

  useEffect(() => {
    filterRef.current = {
      date: flightListingFilter?.date,
      keyword: flightListingFilter?.keyword,
      terminal: flightListingFilter?.terminal,
      airline: flightListingFilter?.airline,
      airport: flightListingFilter?.airport,
    }
  }, [flightListingFilter])

  useEffect(() => {
    if (isFilterModalVisible || loadedEarlierFlights) {
      cancelLoopApiJob()
    } else {
      startLoopApiCall(handleRefresh)
    }
  }, [isFilterModalVisible, loadedEarlierFlights])

  useFocusEffect(
    useCallback(() => {
      if (!isAfterLogin.current) {
        onFilter({
          terminal: filterRef.current.terminal,
          airline: filterRef.current.airline,
          airport: filterRef.current.airport,
        })
      } else {
        isAfterLogin.current = false
      }
      isFocusedRef.current = true
      return () => {
        isFocusedRef.current = false
      }
    }, []),
  )

  return (
    <View style={tabScreenStyles.container}>
      <Category
        componentName={COMPONENT_NAME}
        onSearchPress={handleSearchPress}
        filter={{
          terminal: flightListingFilter.terminal,
          direction: flightListingFilter.direction,
          airline: flightListingFilter.airline,
          cityAirport: flightListingFilter.airport,
        }}
        onFilterFlight={(options) => {
          dispatch(
            FlightListingCreators.setFlightListingFilter({
              ...flightListingFilter,
              terminal: options.terminal,
              airline: options.airline,
              airport: options.cityAirport,
            }),
          )
          onFilter({
            terminal: options.terminal,
            airline: options.airline,
            airport: options.cityAirport,
          })
        }}
        isShowDirection={false}
        isPaneAbsoluteToRoot={false}
        containerStyle={tabScreenStyles.filterContainer}
        onFacetStateChange={onFacetStateChange}
      />
      <FlightListToolbar onGetEarlierFlights={onGetEarlierFlights} componentName={COMPONENT_NAME} />
      {isLoadingEarlierFlights && !(isEndEarlierFlights) && <LoadingIndicator />}
      {isEndEarlierFlights && (
        <Text
          tx={"flightLanding.noEarlierFlights"}
          preset={"caption2Bold"}
          style={tabScreenStyles.noEarlierFlights}
        ></Text>
      )}
      <AnimatedSectionList
        sections={sectionList}
        keyExtractor={(_item, index) =>
          `${(_item as any)?.flightNumber} ${(_item as any)?.scheduledDate} ${index.toString()}`
        }
        // @ts-ignore
        renderSectionHeader={renderSectionHeader}
        stickySectionHeadersEnabled={false}
        renderItem={onRenderItem}
        ListFooterComponent={footerListFlight}
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
        onEndReachedThreshold={0.4}
        onEndReached={handleLoadMore}
        maxToRenderPerBatch={15}
        style={tabScreenStyles.listContainer}
        windowSize={15}
        ref={scrollRef}
      />
      <FeedBackToast
        ref={toastForRemoveFlight}
        style={tabScreenStyles.feedBackToastStyle}
        textButtonStyle={tabScreenStyles.toastButtonStyle}
        position={"custom"}
        positionValue={{ bottom: 8 }}
        textStyle={tabScreenStyles.toastTextStyle}
        type={FeedBackToastType.smallFeedBack}
        text={msg50?.message || translate("flyLanding.removeFlight")}
      />
      <AlertApp ref={alertApp} />
      {isLoading && (
        <View style={tabScreenStyles.lottieLoadingBackground}>
          <LottieView
            style={tabScreenStyles.lottieLoading}
            source={LoadingAnimation}
            autoPlay
            loop
          />
        </View>
      )}
    </View>
  )
}

export default ArrivalScreen
