import ShimmerPlaceholder from '../../helpers/shimmer-placeholder';
import { color } from 'app/theme/color';
import { PLACEHOLDER_ANIMATION_SPEED_IN_MS } from 'app/utils/constants';
import { Cross } from 'assets/icons';
import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

const LOADING_COLORS = [color.palette.lighterGrey, color.background, color.palette.lighterGrey]

const LoadingV2 = React.memo((props: any) => {
  const { navigationRoute, onClose } = props;
  return (
    <View style={styles.container}>
      <View style={styles.viewImageLoading}>
        <TouchableOpacity style={styles.buttonClose}
          onPress={onClose}
        >
          <Cross />
        </TouchableOpacity>
      </View>
      <View style={styles.viewContent}>
        <ShimmerPlaceholder
          navigationRoute={navigationRoute}
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={LOADING_COLORS}
          shimmerStyle={styles.loadingTitle}
        />
        <ShimmerPlaceholder
          navigationRoute={navigationRoute}
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={LOADING_COLORS}
          shimmerStyle={styles.loadingSubTitle}
        />
        <ShimmerPlaceholder
          navigationRoute={navigationRoute}
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={LOADING_COLORS}
          shimmerStyle={styles.loadingContent}
        />
        <ShimmerPlaceholder
          navigationRoute={navigationRoute}
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={LOADING_COLORS}
          shimmerStyle={styles.loadingContent}
        />
        <ShimmerPlaceholder
          navigationRoute={navigationRoute}
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={LOADING_COLORS}
          shimmerStyle={styles.loadingSubContent}
        />
      </View>
    </View>
  )
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  viewImageLoading: {
    width: '100%',
    height: 250,
    backgroundColor: color.palette.lighterGrey
  },
  viewContent: {
    padding: 24
  },
  loadingTitle: {
    width: '100%',
    height: 20,
    borderRadius: 4
  },
  loadingSubTitle: {
    width: 160,
    height: 20,
    borderRadius: 4,
    marginTop: 12,
    marginBottom: 16
  },
  loadingContent: {
    height: 12,
    width: '100%',
    marginBottom: 12,
    borderRadius: 4,
  },
  loadingSubContent: {
    height: 12,
    width: 80,
    borderRadius: 4,
  },
  buttonClose: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "#FCFCFC99",
    position: 'absolute',
    top: 18,
    right: 14,
    justifyContent: 'center',
    alignItems: 'center'
  }
})

export { LoadingV2 }