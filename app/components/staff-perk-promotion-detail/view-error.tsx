import React from 'react';
import { Platform, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Text } from 'app/elements/text';
import LinearGradient from 'react-native-linear-gradient';
import { Button } from "app/elements/button/button"
import { color, typography } from 'app/theme';
import { useSelector } from 'react-redux';
import { AemSelectors } from 'app/redux/aemRedux';
import { Cross, ErrorCloudV2 } from 'assets/icons';
import BaseImage from 'app/elements/base-image/base-image';
import { mappingUrlAem } from 'app/utils';

const ViewError = React.memo((props: any) => {
  const dataCommonAEM = useSelector(AemSelectors.getErrorsCommon)
  const ehr42 = dataCommonAEM?.find((e) => e?.code === "EHR42")
  const { handlePressReload, isNoInternet, onCloseModal } = props;

  const renderContent = () => {
    const titleTx = ehr42?.header ? ehr42?.header : "errorOverlay.variant1.title"
    const messageTx = ehr42?.subHeader ? ehr42?.subHeader : "errorOverlay.variant1.message"
    const reloadTx = ehr42?.buttonLabel ? ehr42?.buttonLabel : "errorOverlay.variant1.reload"
    return {
      titleTx: titleTx,
      messageTx: messageTx,
      reloadTx: reloadTx
    }
  }

  if (isNoInternet) {
    return (<View style={styles.container}>
      <ErrorCloudV2 />
      <Text style={styles.titleTextStyle} tx={"errorOverlay.variant3.title"} />
      <Text style={styles.messageTextStyle} tx={"errorOverlay.variant3.message"} />
      <LinearGradient
        style={styles.reloadButtonStyle}
        start={{ x: 1, y: 0 }}
        end={{ x: 0, y: 1 }}
        colors={[
          color.palette.gradientColor1Start,
          color.palette.gradientColor1End,
        ]}
      >
        <Button
          onPress={handlePressReload}
          sizePreset="large"
          textPreset="buttonLarge"
          typePreset="primary"
          tx={"errorOverlay.variant3.retry"}
          backgroundPreset="light"
          statePreset="default"
        />
      </LinearGradient>
    </View>)
  }

  return (
    <View style={styles.container}>
      {ehr42?.icon ? (
        <BaseImage source={{ uri: mappingUrlAem(ehr42?.icon) }} style={styles.image} resizeMode='contain' />
      ) : (
        <ErrorCloudV2 />)}
      <Text style={styles.titleTextStyle} tx={ehr42 ? null : renderContent()?.titleTx}
        text={ehr42 ? renderContent()?.titleTx : null}
      />
      <Text style={styles.messageTextStyle} tx={ehr42 ? null : renderContent()?.messageTx}
        text={ehr42 ? renderContent()?.messageTx : null}
      />
      <LinearGradient
        style={styles.reloadButtonStyle}
        start={{ x: 1, y: 0 }}
        end={{ x: 0, y: 1 }}
        colors={[
          color.palette.gradientColor1Start,
          color.palette.gradientColor1End,
        ]}
      >
        <Button
          onPress={handlePressReload}
          sizePreset="large"
          textPreset="buttonLarge"
          typePreset="primary"
          tx={ehr42 ? null : renderContent()?.reloadTx}
          text={ehr42 ? renderContent()?.reloadTx : null}
          backgroundPreset="light"
          statePreset="default"
        />
      </LinearGradient>
      <TouchableOpacity style={styles.buttonClose} onPress={onCloseModal}>
        <Cross width={30} height={30}/>
      </TouchableOpacity>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    paddingTop: 88,
    height: "100%",
    overflow: "hidden",
    backgroundColor: color.palette.lightestGrey,
    paddingHorizontal: 24,
    alignItems: 'center',
    borderWidth: 1
  },
  titleTextStyle: {
    fontFamily: typography.black,
    color: color.palette.almostBlackGrey,
    fontSize: 18,
    fontWeight: Platform.select({ ios: "900", android: "normal" }),
    lineHeight: 22,
    marginBottom: 16,
    marginTop: 40,
    textAlign: "center",
  },
  messageTextStyle: {
    fontFamily: typography.regular,
    color: color.palette.darkestGrey,
    fontSize: 14,
    fontWeight: Platform.select({ ios: "400", android: "normal" }),
    lineHeight: 18,
    textAlign: "center",
  },
  reloadButtonStyle: {
    width: '100%',
    borderRadius: 60,
    paddingHorizontal: 24,
    marginTop: 24
  },
  image: {
    width: 120,
    height: 120
  },
  buttonClose: {
    borderRadius: 12,
    position: 'absolute',
    top: 14,
    right: 10,
  }
})

export { ViewError } 