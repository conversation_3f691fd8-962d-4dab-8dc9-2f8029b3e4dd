import { MMKV } from 'react-native-mmkv';
const mmkvStorage = new MMKV();

export enum ENUM_STORAGE_MMKV {
  CURRENT_SCREEN_ACTIVE = "current_screen_active",
  PREVIOUS_SCREEN = "previous_screen",
  IS_FIRST_APP = "isFirstApp",
  IS_SHOW_MODAL_CHECK_RATING_POPUP = "IsShowModalCheckRatingPopup",
  APP_SETTINGS_VERSION = "appSettingsVersion",
  APP_SETTINGS_REFRESH_STATUS = "appSettingsRefreshStatus",
  IS_SHOWING_LOGIN = "isShowingLogin",
  IS_SHOWING_SESSION_POPUP = "isShowingSessionPopup",
  HAS_SHOWED_NPS_POPUP = "hasShowedNpsPopup",
  NEW_CIAM = "newCiam",
  USER_PASSWORD = "userPassword",
  IS_NAVIGATING_TO_SCREEN = "isNavigatingToScreen",
  IS_ALL_INITIAL_PROMPTS_ARE_DONE = "isAllInitialPromptsAreDone",
  IS_PREVENT_NPS_PROMPT = "isPreventNPSPrompt",
  ANIMATION_IN_DAY = "animationInDay",
  ANIMATION_IN_DAY_PARKING_LEFT = "animationInDayParkingLeft",
  ANIMATION_IN_DAY_PARKING_RIGHT = "animationInDayParkingRight",
  IS_FINISHED_JOURNEY_ONBOARDING = "isFinishJourneyOnboarding",
  IS_ANDROID_BIOMETRICS_PROMPT_SHOWN = "isAndroidBiometricPromptShown",
  FORCED_UPDATE_TEMP_DATA = "forcedUpdateTempData",
  LAST_SAVED_FLIGHT_TIMESTAMP = "lastSavedFlightTimestamp",
  PAGE_CONFIG_LAST_UPDATE = "pageConfigLastUpdate",
  IS_BOT_SIGNUP = "isBotSignUp",
  APP_USER_AGENT = "appUserAgent",
  THROTTLE_REGISTER_ATTEMPT = "throttleRegisterAttempt",
  IS_PLAYPASS_ACTIVE_FINISHED = "isPlayPassActiveFinished",
  SAVED_FLIGHT_PRIOR_ACTIONS = "savedFlightPriorActions",
  LOCATION_PERMISSION_PREFERENCE_STATE = "locationPermissionPreferenceState",
  FIRST_FETCH_REMOTE_CONFIG = "firstFetchRemoteConfig",
  DATA_DINE_DIRECTORY_IN_DAY = "dataDineDirectoryInDay",
  DATA_DATE_DIRECTORY = "dataDateDirectory",
  FIRST_SET_DEVICE_ID_FOR_BRAZE = "firstSetDeviceIdForBraze",
}

export enum ENUM_STORAGE_TYPE {
  string = "string",
  boolean = "boolean",
  number = "number"
}

const keysToRemove = [
  ENUM_STORAGE_MMKV.CURRENT_SCREEN_ACTIVE,
  ENUM_STORAGE_MMKV.PREVIOUS_SCREEN,
  ENUM_STORAGE_MMKV.IS_FIRST_APP,
  ENUM_STORAGE_MMKV.IS_SHOW_MODAL_CHECK_RATING_POPUP,
  ENUM_STORAGE_MMKV.IS_SHOWING_LOGIN,
  ENUM_STORAGE_MMKV.IS_SHOWING_SESSION_POPUP,
  ENUM_STORAGE_MMKV.HAS_SHOWED_NPS_POPUP,
  ENUM_STORAGE_MMKV.FORCED_UPDATE_TEMP_DATA,
  ENUM_STORAGE_MMKV.IS_PLAYPASS_ACTIVE_FINISHED,
]

export const removeMMKVStorage = () => {
  keysToRemove.forEach(key => {
    mmkvStorage.delete(key);
  });
}

export const setSyncTaskInfo = (key, data: string) => {
  setMMKVdata(key, data)
}
export const getSyncTaskInfo = (key) => {
  const data = getMMKVdata(key, ENUM_STORAGE_TYPE.string)
  return data}
  
export const setMMKVdata = (key: ENUM_STORAGE_MMKV, data: string | boolean | number) => {
  mmkvStorage.set(key, data);
}

export const getMMKVdata = (key: ENUM_STORAGE_MMKV, type: ENUM_STORAGE_TYPE) => {
  try {
    if (type === ENUM_STORAGE_TYPE.string) {
      return mmkvStorage.getString(key)
    } else if (type === ENUM_STORAGE_TYPE.boolean) {
      return mmkvStorage.getBoolean(key)
    } else if (type === ENUM_STORAGE_TYPE.number) {
      return mmkvStorage.getNumber(key)
    }
    return null
  } catch (error) {
    console.error(error.message || "Something went wrong with getMMKVdata")
    return null
  }
}

export const setIsShowModalCheckRatingPopup = (data) => {
  setMMKVdata(ENUM_STORAGE_MMKV.IS_SHOW_MODAL_CHECK_RATING_POPUP, data)
}
export const getIsShowModalCheckRatingPopup = () => {
  return getMMKVdata(ENUM_STORAGE_MMKV.IS_SHOW_MODAL_CHECK_RATING_POPUP, ENUM_STORAGE_TYPE.boolean)
}
export const setIsShowingLogin = (data) => {
  setMMKVdata(ENUM_STORAGE_MMKV.IS_SHOWING_LOGIN, data)
}
export const getIsShowingLogin = () => {
  return getMMKVdata(ENUM_STORAGE_MMKV.IS_SHOWING_LOGIN, ENUM_STORAGE_TYPE.boolean)
}
export const setIsShowingSessionPopup = (data) => {
  setMMKVdata(ENUM_STORAGE_MMKV.IS_SHOWING_SESSION_POPUP, data)
}
export const getIsShowingSessionPopup = () => {
  return getMMKVdata(ENUM_STORAGE_MMKV.IS_SHOWING_SESSION_POPUP, ENUM_STORAGE_TYPE.boolean)
}
export const setHasShowedNPS = (data) => {
  setMMKVdata(ENUM_STORAGE_MMKV.HAS_SHOWED_NPS_POPUP, data)
}
export const getHasShowedNPS = () => {
  return getMMKVdata(ENUM_STORAGE_MMKV.HAS_SHOWED_NPS_POPUP, ENUM_STORAGE_TYPE.boolean)
}
export const setUserPassword = (password: string) => {
  setMMKVdata(ENUM_STORAGE_MMKV.USER_PASSWORD, password)
}
export const getUserPassword = () => {
  return getMMKVdata(ENUM_STORAGE_MMKV.USER_PASSWORD, ENUM_STORAGE_TYPE.string)
}

// APP SETTINGS
export const setAppSettingVersion = (data = "") => {
  setMMKVdata(ENUM_STORAGE_MMKV.APP_SETTINGS_VERSION, data)
}
export const getAppSettingVersion = () => {
  const data = getMMKVdata(ENUM_STORAGE_MMKV.APP_SETTINGS_VERSION, ENUM_STORAGE_TYPE.string)
  return data
}
export const setAppSettingNeedToRefreshStatus = (data: boolean) => {
  setMMKVdata(ENUM_STORAGE_MMKV.APP_SETTINGS_REFRESH_STATUS, data)
}
export const getAppSettingNeedToRefreshStatus = () => {
  const data = getMMKVdata(ENUM_STORAGE_MMKV.APP_SETTINGS_REFRESH_STATUS, ENUM_STORAGE_TYPE.boolean)
  return data
}
export const setAnimationInDay = (data: string) => {
  setMMKVdata(ENUM_STORAGE_MMKV.ANIMATION_IN_DAY, data)
}
export const getAnimationInDay = () => {
  const data = getMMKVdata(ENUM_STORAGE_MMKV.ANIMATION_IN_DAY, ENUM_STORAGE_TYPE.string)
  return data
}

export const setAnimationInDayParkingLeft = (data: string) => {
  setMMKVdata(ENUM_STORAGE_MMKV.ANIMATION_IN_DAY_PARKING_LEFT, data)
}

export const getAnimationInDayParkingLeft = () => {
  const data = getMMKVdata(ENUM_STORAGE_MMKV.ANIMATION_IN_DAY_PARKING_LEFT, ENUM_STORAGE_TYPE.string)
  return data
}

export const setAnimationInDayParkingRight = (data: string) => {
  setMMKVdata(ENUM_STORAGE_MMKV.ANIMATION_IN_DAY_PARKING_RIGHT, data)
}

export const getAnimationInDayParkingRight = () => {
  const data = getMMKVdata(ENUM_STORAGE_MMKV.ANIMATION_IN_DAY_PARKING_RIGHT, ENUM_STORAGE_TYPE.string)
  return data
}

export const getDataDineDirectoryInday = () => {
  const data = getMMKVdata(ENUM_STORAGE_MMKV.DATA_DINE_DIRECTORY_IN_DAY, ENUM_STORAGE_TYPE.string)
  return data
}

export const setDataDineDirectoryInday = (data: string) => {
  setMMKVdata(ENUM_STORAGE_MMKV.DATA_DINE_DIRECTORY_IN_DAY, data)
}

export const getDateSaveDineDirectoryInday = () => {
  const data = getMMKVdata(ENUM_STORAGE_MMKV.DATA_DATE_DIRECTORY, ENUM_STORAGE_TYPE.string)
  return data
}

export const setDateSaveDineDirectoryInday = (data: string) => {
  setMMKVdata(ENUM_STORAGE_MMKV.DATA_DATE_DIRECTORY, data)
}

// Check if all initial permission prompts are confirmed
export const setAllInitialPromptsAreDone = (data: boolean) => {
  setMMKVdata(ENUM_STORAGE_MMKV.IS_ALL_INITIAL_PROMPTS_ARE_DONE, data)
}
export const getAllInitialPromptsAreDone = () => {
  const data = getMMKVdata(ENUM_STORAGE_MMKV.IS_ALL_INITIAL_PROMPTS_ARE_DONE, ENUM_STORAGE_TYPE.boolean)
  return data
}

// Flag to prevent showing NPS prompt
export const setPreventNPSPrompt = (data: boolean) => {
  setMMKVdata(ENUM_STORAGE_MMKV.IS_PREVENT_NPS_PROMPT, data)
}
export const getPreventNPSPrompt = () => {
  const data = getMMKVdata(ENUM_STORAGE_MMKV.IS_PREVENT_NPS_PROMPT, ENUM_STORAGE_TYPE.boolean)
  return data
}


// temp data for forcedUpdate
export const setForcedUpdateTempData = (data) => {
  const tempData = data ? JSON.stringify(data) : ""
  setMMKVdata(ENUM_STORAGE_MMKV.FORCED_UPDATE_TEMP_DATA, tempData)
}
export const getForcedUpdateTempData = () => {
  const data = getMMKVdata(ENUM_STORAGE_MMKV.FORCED_UPDATE_TEMP_DATA, ENUM_STORAGE_TYPE.string)
  if (data) return JSON.parse(data as string)
  return null
}

export const setLastSavedFlightTime = (time: number) => {
  setMMKVdata(ENUM_STORAGE_MMKV.LAST_SAVED_FLIGHT_TIMESTAMP, time)
}
export const getLastSavedFlightTime = () => {
  return getMMKVdata(ENUM_STORAGE_MMKV.LAST_SAVED_FLIGHT_TIMESTAMP, ENUM_STORAGE_TYPE.number) || 0
}

export const setUserAgent = (data: string) => {
  setMMKVdata(ENUM_STORAGE_MMKV.LAST_SAVED_FLIGHT_TIMESTAMP, data)
}
export const getUserAgent = () => {
  return getMMKVdata(ENUM_STORAGE_MMKV.LAST_SAVED_FLIGHT_TIMESTAMP, ENUM_STORAGE_TYPE.string) || ""
}

export const setThrottleRegisterAttempt = (data: any) => {
  const tempData = data ? JSON.stringify(data) : ""
  setMMKVdata(ENUM_STORAGE_MMKV.THROTTLE_REGISTER_ATTEMPT, tempData)
}
export const getThrottleRegisterAttempt = () => {
  const data = getMMKVdata(ENUM_STORAGE_MMKV.THROTTLE_REGISTER_ATTEMPT, ENUM_STORAGE_TYPE.string)
  if (data) return JSON.parse(data as string)
  return null
}

export const setPlayPassBookingFinished = (data: boolean) => {
  setMMKVdata(ENUM_STORAGE_MMKV.IS_PLAYPASS_ACTIVE_FINISHED, data)
}
export const getPlayPassBookingFinished = () => {
  return getMMKVdata(ENUM_STORAGE_MMKV.IS_PLAYPASS_ACTIVE_FINISHED, ENUM_STORAGE_TYPE.boolean) || false
}

export const setLocationPermissionPreferenceState = (data: any) => {
  const tempData = data ? JSON.stringify(data) : ""
  setMMKVdata(ENUM_STORAGE_MMKV.LOCATION_PERMISSION_PREFERENCE_STATE, tempData)
}
export const getLocationPermissionPreferenceState = () => {
  const data = getMMKVdata(ENUM_STORAGE_MMKV.LOCATION_PERMISSION_PREFERENCE_STATE, ENUM_STORAGE_TYPE.string)
  if (data) return JSON.parse(data as string)
  return null
}

export const getFirstFetchRemoteConfig = () => {
  const data = getMMKVdata(ENUM_STORAGE_MMKV.FIRST_FETCH_REMOTE_CONFIG, ENUM_STORAGE_TYPE.boolean)
  return data
}
export const setFirstFetchRemoteConfig = (data: boolean) => {
  setMMKVdata(ENUM_STORAGE_MMKV.FIRST_FETCH_REMOTE_CONFIG, data)
} 

export const getFirstSetDeviceIDForBraze = () => {
  const data = getMMKVdata(ENUM_STORAGE_MMKV.FIRST_SET_DEVICE_ID_FOR_BRAZE, ENUM_STORAGE_TYPE.boolean)
  return data
}
export const setFirstSetDeviceIDForBraze = (data: boolean) => {
  setMMKVdata(ENUM_STORAGE_MMKV.FIRST_SET_DEVICE_ID_FOR_BRAZE, data)
}
